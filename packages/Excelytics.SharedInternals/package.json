{"description": "Shared types, enums, constants, middleware and schemas for microservices.", "name": "excelytics.shared-internals", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "version": "1.6.1", "type": "module", "author": "<PERSON><PERSON>", "license": "UNLICENSED", "scripts": {"clean": "rm -rf dist", "compile:js": "tsup", "compile:dts": "npx tsc -p . --emitDeclarationOnly", "build-old": "bun run clean && bun run compile:js && bun run compile:dts", "build": "bun run clean && tsc", "test": "bun test src/__tests__", "test:ci": "ENV=test NODE_ENV=test bun test src/__tests__", "test:watch": "bun test --watch src/__tests__", "test:coverage": "bun test --coverage src/__tests__", "test:error-handler": "bun test src/__tests__/error-handler.test.ts", "test:integration": "bun test src/__tests__/integration.test.ts", "test:helpers": "bun test src/__tests__/error-helpers.test.ts", "test:all": "bun run test:error-handler && bun run test:helpers && bun run test:integration", "pretest": "bun run build", "prepublishOnly": "bun run test && bun run build", "version": "bun version", "publish:package": "bun install && bun run build && bun publish"}, "exports": {".": "./dist/index.js", "./config": "./dist/config/index.js", "./constants": "./dist/constants/index.js", "./enums": "./dist/enums/index.js", "./errors": "./dist/errors/index.js", "./helpers": "./dist/helpers/index.js", "./interfaces": "./dist/interfaces/index.js", "./middleware": "./dist/middleware/index.js", "./notifications": "./dist/notifications/index.js", "./schemas": "./dist/schemas/index.js", "./services": "./dist/services/index.js", "./types": "./dist/types/index.js", "./utils": "./dist/utils/index.js"}, "keywords": ["shared", "types", "enums", "constants", "schemas", "errors", "helpers", "middleware", "utils", "services", "config", "notifications"], "files": ["dist", "src"], "devDependencies": {"@types/axios": "^0.14.4", "@types/express": "^5.0.3", "@types/mongoose": "^5.11.97", "@types/multer": "^1.4.13", "@types/node": "^22.15.33", "@types/supertest": "^6.0.3", "bun": "^1.2.17", "bun-types": "^1.2.17", "dts-bundle-generator": "^9.5.1", "supertest": "^7.1.1", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "dependencies": {"@typegoose/typegoose": "^12.17.0", "@types/jsonwebtoken": "^9.0.10", "axios": "^1.10.0", "express": "^5.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mongoose": "8.16.0", "multer": "^2.0.1", "zod": "^3.25.67"}, "engines": {"bun": ">=1.0.0"}, "publishConfig": {"registry": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/"}}