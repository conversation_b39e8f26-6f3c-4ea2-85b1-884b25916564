/**
 * Token helper functions.
 * Provides methods for working with tokens, such as extracting from headers.
 * @module token.helper
 */

/**
 * Extract Bearer token from Authorization header
 */
function ExtractBearerToken(authHeader: string): string | null {
	const parts = authHeader.split(' ');
	if (parts.length !== 2 || parts[0] !== 'Bearer') {
		return null;
	}

	return parts[1];
}

export const TokenHelper = {
	ExtractBearerToken
};