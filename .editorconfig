# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = false
trim_trailing_whitespace = true

# TypeScript, JavaScript, JSON
[*.{ts,tsx,js,jsx,mjs,cjs}]
indent_style = tab
indent_size = 4

# JSON files
[*.{json,jsonc}]
indent_style = space
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Markdown files
[*.{md,mdx}]
indent_style = space
indent_size = 2
max_line_length = 80

# Package.json
[package.json]
indent_style = space
indent_size = 4

# Config files
[*.{rc,config}]
indent_style = space
indent_size = 2

# Shell scripts
[*.{sh,bash}]
indent_style = space
indent_size = 2

# Docker files
[{Dockerfile*,*.dockerfile}]
indent_style = space
indent_size = 2
