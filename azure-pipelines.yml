# Trigger on changes to the master branch
trigger:
  - master

pr:
  branches:
    include:
      - master

# Link the Variable Group
variables:
  - group: SharedFeedCredentials

# Run this pipeline on a self-hosted agent (unraid server)
pool:
  name: 'Unraid'

jobs:
  - job: LintBuild
    displayName: 'Lint and Build Job'
    steps:
      # Step 1: Checkout the code
      - checkout: self
        displayName: 'Checkout code'

      # Step 2: Setup Node.js environment
      - task: NodeTool@0
        displayName: 'Setup Node.js 22.11.0'
        inputs:
          versionSpec: '22.11.0'

      # Step 3: Install Bun globally
      - task: Npm@1
        displayName: 'Setup Bun Globally'
        inputs:
          command: 'custom'
          customCommand: 'install -g bun'
          verbose: true

      # Step 4: Create bunfig.toml using the PAT from the Variable Group
      - script: |
          echo "Creating bunfig.toml for Azure DevOps feed..."
          # Bun will then read this $ADO_PAT environment variable.
          echo '[install.registry]' > bunfig.toml
          echo '"pkgs.dev.azure.com" = { token = "$ADO_PAT" }' >> bunfig.toml
          # This configures Bun to use the ADO_PAT env var for any requests to pkgs.dev.azure.com
          echo "--- bunfig.toml content ---"
          cat bunfig.toml
          echo "---------------------------"
        displayName: 'Create bunfig.toml for Artifacts'
        env:
          ADO_PAT: $(FEED_PAT_READ) # Map the pipeline variable from the group to ADO_PAT env var

      # Step 5: Install project dependencies using Bun
      # Optional: Delete bun.lock first for a clean resolution test
      - script: |
          echo "Installing project dependencies using Bun..."
          echo "Current directory: $(pwd)"
          echo "Deleting client/bun.lock..."
          rm -f bun.lock
          bun install
        displayName: 'Install Project Dependencies (Bun)'

        # Step 6: Run ESLint using Bun
      - script: |
          echo "Running ESLint in $(pwd)..."
          bunx eslint .
        displayName: 'Run ESLint (Bunx)'

      # Step 7: Run unit tests using Bun
      # - script: |
      #     echo "Step 9: Running unit tests for backend using Bun..."
      #     bun test
      #   displayName: 'Run Backend Unit Tests (Bun)'

      # Step 8: Publish Markdown file to Azure DevOps
      # https://marketplace.visualstudio.com/items?itemName=MasamitsuMurase.publish-markdown-reports&targetId=976c6d15-ab4a-40f0-b66f-408c3e74ebaf
      - task: PublishMarkdownReports@1
        inputs:
          contentPath: $(Build.SourcesDirectory)/shared
          indexFile: 2025-06-18_Introspection.Identity_structure_deep.md
          latexFormula: true