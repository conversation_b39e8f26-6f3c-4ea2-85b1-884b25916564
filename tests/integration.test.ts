import { beforeAll, describe, afterAll, expect, it } from 'bun:test';
import { EnumClientOrigin } from 'excelytics.shared-internals';
import supertest from 'supertest';
import mongoose from 'mongoose';
import app from '@/server';

const request = supertest(app);

// Integration test user
const integrationUser = {
	clientId: new mongoose.Types.ObjectId(),
	clientOrigin: EnumClientOrigin.Excelytics,
	email: '<EMAIL>',
	password: 'IntegrationTest@123',
	isActive: true
};

let userAccessToken: string;
let userRefreshToken: string;

describe('End-to-End Integration Tests', () => {
	beforeAll(async () => {
		// Clean up any existing test user
		await request.delete(`/api/v1/identity/email/${integrationUser.email}`);
	});

	afterAll(async () => {
		// Clean up test user
		console.log('Cleaning up integration test user');
		await request.delete(`/api/v1/identity/email/${integrationUser.email}`);
	});

	describe('Complete Authentication Flow', () => {
		it('Should complete full user registration flow', async () => {
			const response = await request
				.post('/api/v1/auth/register')
				.send(integrationUser);

			expect(response.status).toBe(200);
			expect(response.body).toHaveProperty('success', true);
			expect(response.body.data).toHaveProperty('token');
			expect(response.body.data).toHaveProperty('refreshToken');
			expect(response.body.data).toHaveProperty('tokenPayload');

			// Store tokens for subsequent tests
			userAccessToken = response.body.data.token;
			userRefreshToken = response.body.data.refreshToken;

			// Verify token payload structure
			expect(response.body.data.tokenPayload).toHaveProperty('userId');
			expect(response.body.data.tokenPayload).toHaveProperty('email', integrationUser.email);
			expect(response.body.data.tokenPayload).toHaveProperty('clientId');
		});

		it('Should prevent duplicate user registration', async () => {
			const response = await request
				.post('/api/v1/auth/register')
				.send(integrationUser);

			expect(response.status).toBe(409); // Conflict - user already exists
		});

		it('Should complete login flow with registered user', async () => {
			const loginData = {
				email: integrationUser.email,
				password: integrationUser.password
			};

			const response = await request
				.post('/api/v1/auth/login')
				.send(loginData);

			expect(response.status).toBe(200);
			expect(response.body).toHaveProperty('success', true);
			expect(response.body.data).toHaveProperty('token');
			expect(response.body.data).toHaveProperty('refreshToken');

			// Update tokens
			userAccessToken = response.body.data.token;
			userRefreshToken = response.body.data.refreshToken;
		});

		it('Should validate access token through introspection', async () => {
			const response = await request
				.post('/api/v1/auth/introspect')
				.send({ token: userAccessToken });

			expect(response.status).toBe(200);
			expect(response.body).toHaveProperty('active', true);
			expect(response.body).toHaveProperty('sub');
			expect(response.body).toHaveProperty('username', integrationUser.email);
			expect(response.body).toHaveProperty('iss', 'identity');
		});

		it('Should refresh access token successfully', async () => {
			const response = await request
				.post('/api/v1/auth/refresh-token')
				.send({ refreshToken: userRefreshToken });

			expect(response.status).toBe(200);
			expect(response.body).toHaveProperty('success', true);
			expect(response.body.data).toHaveProperty('token');
			
			// Verify new token is different
			expect(response.body.data.token).not.toBe(userAccessToken);
			
			// Update access token
			userAccessToken = response.body.data.token;
		});
	});

	describe('Token Lifecycle Management', () => {
		it('Should use refreshed token for authenticated requests', async () => {
			// Try to access a protected endpoint with the new token
			const response = await request
				.post('/api/v1/verify-access-token')
				.send({ token: userAccessToken });

			expect(response.status).toBe(200);
			expect(response.body.data).toHaveProperty('active', true);
		});

		it('Should handle token expiration gracefully', async () => {
			// This would test with an actually expired token
			// For now, test with malformed token
			const response = await request
				.post('/api/v1/verify-access-token')
				.send({ token: 'expired.token.simulation' });

			expect(response.status).toBe(200);
			expect(response.body.data).toHaveProperty('active', false);
		});

		it('Should invalidate tokens after user deactivation', async () => {
			// This test would require admin functionality to deactivate user
			// For now, just verify current token is still valid
			const response = await request
				.post('/api/v1/auth/introspect')
				.send({ token: userAccessToken });

			expect(response.status).toBe(200);
			expect(response.body).toHaveProperty('active', true);
		});
	});

	describe('Cross-Service Token Validation', () => {
		it('Should provide RFC 7662 compliant introspection response', async () => {
			const response = await request
				.post('/api/v1/auth/introspect')
				.send({ token: userAccessToken });

			expect(response.status).toBe(200);
			
			// RFC 7662 required fields
			expect(response.body).toHaveProperty('active', true);
			
			// RFC 7662 optional fields that we implement
			expect(response.body).toHaveProperty('sub'); // Subject
			expect(response.body).toHaveProperty('iss'); // Issuer
			expect(response.body).toHaveProperty('iat'); // Issued At
			expect(response.body).toHaveProperty('exp'); // Expires At
			expect(response.body).toHaveProperty('token_type', 'Bearer');
			expect(response.body).toHaveProperty('username');
		});

		it('Should handle service-to-service token validation', async () => {
			// Simulate another microservice validating the token
			const response = await request
				.post('/api/v1/auth/introspect')
				.send({ token: userAccessToken })
				.set('User-Agent', 'Introspection.Finance.Backend/1.0');

			expect(response.status).toBe(200);
			expect(response.body).toHaveProperty('active', true);
		});
	});

	describe('Security Integration Tests', () => {
		it('Should enforce rate limiting across authentication endpoints', async () => {
			const invalidCredentials = {
				email: integrationUser.email,
				password: 'WrongPassword@123'
			};

			// Make multiple failed attempts
			const promises = Array(15).fill(null).map(() =>
				request.post('/api/v1/auth/login').send(invalidCredentials)
			);

			const responses = await Promise.all(promises);
			
			// Should have some rate limited responses
			const rateLimited = responses.filter(res => res.status === 429);
			expect(rateLimited.length).toBeGreaterThan(0);
		});

		it('Should maintain security headers across all endpoints', async () => {
			const endpoints = [
				'/api/v1/health',
				'/api/v1/auth/session'
			];

			for (const endpoint of endpoints) {
				const response = await request.get(endpoint);
				
				expect(response.header['x-frame-options']).toBe('DENY');
				expect(response.header['x-content-type-options']).toBe('nosniff');
			}
		});

		it('Should handle CORS properly across all endpoints', async () => {
			const allowedOrigin = 'http://localhost:6000';
			
			const response = await request
				.post('/api/v1/auth/login')
				.set('Origin', allowedOrigin)
				.send({
					email: integrationUser.email,
					password: integrationUser.password
				});

			expect(response.header['access-control-allow-origin']).toBe(allowedOrigin);
		});
	});

	describe('Error Handling Integration', () => {
		it('Should provide consistent error format across endpoints', async () => {
			// Test invalid login
			const loginResponse = await request
				.post('/api/v1/auth/login')
				.send({ email: '<EMAIL>', password: 'wrong' });

			expect(loginResponse.status).toBeGreaterThanOrEqual(400);
			expect(loginResponse.body).toHaveProperty('success', false);
			expect(loginResponse.body).toHaveProperty('error');

			// Test invalid token introspection
			const introspectResponse = await request
				.post('/api/v1/auth/introspect')
				.send({ token: 'invalid.token' });

			expect(introspectResponse.status).toBe(200);
			expect(introspectResponse.body).toHaveProperty('active', false);
		});

		it('Should handle malformed requests gracefully', async () => {
			const response = await request
				.post('/api/v1/auth/login')
				.send('invalid json');

			expect(response.status).toBe(400);
		});
	});

	describe('Performance Integration Tests', () => {
		it('Should handle concurrent authentication requests', async () => {
			const loginData = {
				email: integrationUser.email,
				password: integrationUser.password
			};

			const promises = Array(10).fill(null).map(() =>
				request.post('/api/v1/auth/login').send(loginData)
			);

			const responses = await Promise.all(promises);
			
			// All should succeed
			responses.forEach(response => {
				expect(response.status).toBe(200);
				expect(response.body.data).toHaveProperty('token');
			});
		});

		it('Should handle concurrent token validation requests', async () => {
			const promises = Array(10).fill(null).map(() =>
				request.post('/api/v1/auth/introspect').send({ token: userAccessToken })
			);

			const responses = await Promise.all(promises);
			
			responses.forEach(response => {
				expect(response.status).toBe(200);
				expect(response.body).toHaveProperty('active', true);
			});
		});

		it('Should respond within acceptable time limits', async () => {
			const startTime = Date.now();
			
			const response = await request
				.post('/api/v1/auth/login')
				.send({
					email: integrationUser.email,
					password: integrationUser.password
				});
			
			const endTime = Date.now();
			
			expect(response.status).toBe(200);
			expect(endTime - startTime).toBeLessThan(2000); // Under 2 seconds
		});
	});
});
