import { EnumClientOrigin } from 'excelytics.shared-internals';
import { beforeAll, describe, expect, it } from 'bun:test';
import IdentityModel from '@/models/identity.model';
import supertest from 'supertest';
import mongoose from 'mongoose';
import app from '@/server';

// Wrap the Express app
const request = supertest(app);

describe('Login Rate-Limiter Test', () => {
	// Ensure the user is registered before running the tests
	beforeAll(async () => {
		const data = {
			clientId: new mongoose.Types.ObjectId(),
			clientOrigin: EnumClientOrigin.Excelytics,
			email: '<EMAIL>',
			password: 'Password@123',
			isActive: true
		};

		const user = await IdentityModel.findOne({ email: data.email }).exec();
		if (!user) {
			console.log('user does not exist, creating...');
			await request.post('/api/v1/auth/register').send(data);
		}
	});
	it('Should apply rate-limiting to 10+ login failures', async () => {
		const data = {
			email: '<EMAIL>',
			password: 'IncorectPassword@123'
		};

		// Make multiple failure login attempts (10 in total)
		for (let i = 0; i < 10; i++) {
			await request.post('/api/v1/auth/login').send(data);
		}

		// Last request should be rate-limited
		const result = await request.post('/api/v1/auth/login').send(data);
		expect(result.status).toBe(429);
		expect(result.text).toContain('Too many failed attempts, please try again later.');
	});
});

describe('CORS Tests', () => {
	it('should enforce CORS headers for allowed origins', async () => {
		const allowedOrigin = 'http://localhost:6000';
		const response = await request.get('/').set('Origin', allowedOrigin);

		expect(response.header['access-control-allow-origin']).toBe(allowedOrigin);
		expect(response.header['access-control-allow-credentials']).toBe('true');
	});

	it('should block CORS requests from disallowed origins', async () => {
		const disallowedOrigin = 'http://example.com';
		const response = await request.get('/').set('Origin', disallowedOrigin);

		expect(response.status).toBe(403); // Forbidden
		expect(response.text).toContain('Not allowed by CORS');
	});
});

describe('Helmet Tests', () => {
	it('should apply Helmet security headers', async () => {
		const response = await request.get('/');

		// Check for some common Helmet headers
		expect(response.header['x-frame-options']).toBe('DENY');
		expect(response.header['x-content-type-options']).toBe('nosniff');
		expect(response.header['x-xss-protection']).toBe('0');
		expect(response.header['x-permitted-cross-domain-policies']).toBe('none');
	});
});

describe('Session Tests', () => {
	it('session middleware should set the cookie', async () => {
		const response = await request.get('/api/v1/auth/session');

		// Expect a "set-cookie" header to be present
		expect(response.header['set-cookie']).toBeDefined();
		expect(response.status).toBe(200);
	});
});
