// Test configuration
export const TEST_CONFIG = {
	// Test timeouts
	DEFAULT_TIMEOUT: 30000, // 30 seconds
	INTEGRATION_TIMEOUT: 60000, // 1 minute for integration tests

	// Test data cleanup
	CLEANUP_ENABLED: true,

	// Test user prefixes to identify test data
	TEST_EMAIL_PREFIX: 'test-',

	// Database settings for tests
	TEST_DB_NAME: 'introspection_identity_test',

	// Rate limiting test settings
	RATE_LIMIT_TEST_ATTEMPTS: 12,
	RATE_LIMIT_TIMEOUT: 5000,

	// Performance test thresholds
	PERFORMANCE_THRESHOLDS: {
		HEALTH_CHECK_MAX_TIME: 1000, // 1 second
		AUTH_REQUEST_MAX_TIME: 2000, // 2 seconds
		TOKEN_VALIDATION_MAX_TIME: 500, // 500ms
		DEEP_HEALTH_CHECK_MAX_TIME: 5000 // 5 seconds
	}
} as const;

export const TEST_VALUES = {
	// For now, we will configure a malformed token that will be rejected
	EXPIRED_TOKEN: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.invalid',

	MALFORMED_TOKEN: 'invalid.jwt.token'
} as const;