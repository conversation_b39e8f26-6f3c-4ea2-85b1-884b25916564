import {
	EnumClientOrigin,
	EnumClientPath
} from 'excelytics.shared-internals';
import mongoose from 'mongoose';

/**
 * Test user interface for creating test users
 * Based on Identity model structure
 */
export interface TestUser {
	clientId: mongoose.Types.ObjectId;
	clientOrigin: EnumClientOrigin;
	clientPath?: EnumClientPath;
	email: string;
	password: string;
	isActive: boolean;
	roles?: string[];
}

/** Test user creation data (matches Identity model) */
export interface TestUserCreateData {
	clientId: string;
	clientOrigin: EnumClientOrigin;
	email: string;
	password: string;
	isActive: boolean;
	roles?: string[];
}

/**
 * Authentication tokens returned from auth operations
 * Uses proper SharedInternals types
 */
export interface AuthTokens {
	accessToken: string;
	refreshToken: string;
	user: TestUserData;
}

/**
 * User data returned from authentication operations
 * Based on Identity model with toJSON transform
 */
export interface TestUserData {
	id: string; // Transformed from _id by Identity model
	email: string;
	clientId: string;
	clientOrigin: EnumClientOrigin;
	isActive: boolean;
	roles: string[];
	lastLogin?: Date;
	createdAt?: Date;
	updatedAt?: Date;
}

/** Health check response.body type */
export interface HealthResponseType {
	success: boolean;
	message: string;
	data: {
		service: string;
		version: string;
		timestamp: string;
		status: string;
		responseTime: number;
		dependencies: {
			name: string;
			status: string;
			error?: string;
			responseTime?: number;
		}[];
	};
}

/** Rate limiting test result */
export interface RateLimitTestResult {
	statusCodes: number[];
	rateLimitHit: boolean;
	firstRateLimitIndex: number;
	totalRequests: number;
}