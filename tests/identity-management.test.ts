import { beforeAll, describe, afterAll, expect, it } from 'bun:test';
import { EnumClientOrigin } from 'excelytics.shared-internals';
import supertest from 'supertest';
import mongoose from 'mongoose';
import app from '@/server';

const request = supertest(app);

// Create unique test users for this test run
const timestamp = Date.now();

// Admin user for testing identity management
const adminUser = {
	clientId: new mongoose.Types.ObjectId(),
	clientOrigin: EnumClientOrigin.Excelytics,
	email: `admin-test-${timestamp}@example.com`,
	password: 'AdminTest@123',
	isActive: true,
	role: 'admin' // Assuming admin role exists
};

// Regular user for testing
const regularUser = {
	clientId: new mongoose.Types.ObjectId(),
	clientOrigin: EnumClientOrigin.Excelytics,
	email: `regular-user-${timestamp}@example.com`,
	password: 'RegularUser@123',
	isActive: true
};

// Test user to be managed
const managedUser = {
	clientId: new mongoose.Types.ObjectId(),
	clientOrigin: EnumClientOrigin.Excelytics,
	email: `managed-user-${timestamp}@example.com`,
	password: 'ManagedUser@123',
	isActive: true
};

let adminAccessToken: string;
let regularAccessToken: string;

describe('Identity Management Tests', () => {
	beforeAll(async () => {
		// Register unique test users for this test run

		// Register admin user and get token
		const adminRegisterResponse = await request
			.post('/api/v1/auth/register')
			.send(adminUser);

		if (adminRegisterResponse.status === 200 || adminRegisterResponse.status === 201) {
			adminAccessToken = adminRegisterResponse.body.data.token;
		} else {
			console.error('Admin registration failed:', adminRegisterResponse.status, adminRegisterResponse.body);
		}

		// Register regular user and get token
		const regularRegisterResponse = await request
			.post('/api/v1/auth/register')
			.send(regularUser);

		if (regularRegisterResponse.status === 200 || regularRegisterResponse.status === 201) {
			regularAccessToken = regularRegisterResponse.body.data.token;
		} else {
			console.error('Regular user registration failed:', regularRegisterResponse.status, regularRegisterResponse.body);
		}

		// Register managed user
		await request.post('/api/v1/auth/register').send(managedUser);
	});

	afterAll(async () => {
		// Note: Test user cleanup would require admin authentication
		// For now, we leave test users in the database (common practice in testing)
		console.log(`Test completed. Users remain in database for debugging:`);
		console.log(`- Admin: ${adminUser.email}`);
		console.log(`- Regular: ${regularUser.email}`);
		console.log(`- Managed: ${managedUser.email}`);
	});

	describe('Authentication Required Tests', () => {
		it('Should reject unauthenticated requests to identity endpoints', async () => {
			const response = await request
				.get(`/api/v1/identity/email/${managedUser.email}`);

			expect(response.status).toBe(401);
		});

		it('Should reject requests with invalid tokens', async () => {
			const response = await request
				.get(`/api/v1/identity/email/${managedUser.email}`)
				.set('Authorization', 'Bearer invalid.token.here');

			expect(response.status).toBe(401);
		});
	});

	describe('Admin Role Required Tests', () => {
		it('Should reject regular user access to identity management', async () => {
			const response = await request
				.get(`/api/v1/identity/email/${managedUser.email}`)
				.set('Authorization', `Bearer ${regularAccessToken}`);

			// Should be 403 (Forbidden) due to admin role requirement
			expect(response.status).toBe(403);
		});

		it('Should allow admin user access to identity management', async () => {
			// Note: This test assumes admin role is properly implemented
			// If admin role is not implemented, this might fail
			const response = await request
				.get(`/api/v1/identity/email/${managedUser.email}`)
				.set('Authorization', `Bearer ${adminAccessToken}`);

			// Should not be 401 or 403 if admin access is properly configured
			expect([200, 404, 500]).toContain(response.status);
		});
	});

	describe('User Lookup Tests', () => {
		it('Should find existing user by email', async () => {
			const response = await request
				.get(`/api/v1/identity/email/${managedUser.email}`)
				.set('Authorization', `Bearer ${adminAccessToken}`);

			if (response.status === 200) {
				expect(response.body).toHaveProperty('success', true);
				expect(response.body.data).toHaveProperty('email', managedUser.email);
				expect(response.body.data).not.toHaveProperty('password'); // Should not expose password
			}
		});

		it('Should return 404 for non-existent user', async () => {
			const response = await request
				.get('/api/v1/identity/email/<EMAIL>')
				.set('Authorization', `Bearer ${adminAccessToken}`);

			expect(response.status).toBe(404);
		});

		it('Should validate email format in lookup', async () => {
			const response = await request
				.get('/api/v1/identity/email/invalid-email-format')
				.set('Authorization', `Bearer ${adminAccessToken}`);

			expect(response.status).toBe(400);
		});
	});

	describe('User Management Operations', () => {
		it('Should allow admin to update user status', async () => {
			// This test assumes there's an endpoint to update user status
			const updateData = { isActive: false };
			
			const response = await request
				.patch(`/api/v1/identity/email/${managedUser.email}`)
				.set('Authorization', `Bearer ${adminAccessToken}`)
				.send(updateData);

			// Response depends on implementation
			expect([200, 404, 501]).toContain(response.status);
		});

		it('Should allow admin to delete user', async () => {
			// Create a temporary user for deletion test with unique email
			const tempUser = {
				clientId: new mongoose.Types.ObjectId(),
				clientOrigin: EnumClientOrigin.Excelytics,
				email: `temp-delete-${Date.now()}@example.com`,
				password: 'TempDelete@123',
				isActive: true
			};

			await request.post('/api/v1/auth/register').send(tempUser);

			const response = await request
				.delete(`/api/v1/identity/email/${tempUser.email}`)
				.set('Authorization', `Bearer ${adminAccessToken}`);

			expect([200, 204]).toContain(response.status);
		});

		it('Should prevent regular users from deleting accounts', async () => {
			const response = await request
				.delete(`/api/v1/identity/email/${managedUser.email}`)
				.set('Authorization', `Bearer ${regularAccessToken}`);

			expect(response.status).toBe(403);
		});
	});

	describe('User Data Security Tests', () => {
		it('Should not expose sensitive user data', async () => {
			const response = await request
				.get(`/api/v1/identity/email/${managedUser.email}`)
				.set('Authorization', `Bearer ${adminAccessToken}`);

			if (response.status === 200) {
				expect(response.body.data).not.toHaveProperty('password');
				expect(response.body.data).not.toHaveProperty('passwordHash');
				expect(response.body.data).not.toHaveProperty('salt');
			}
		});

		it('Should include only necessary user information', async () => {
			const response = await request
				.get(`/api/v1/identity/email/${managedUser.email}`)
				.set('Authorization', `Bearer ${adminAccessToken}`);

			if (response.status === 200) {
				expect(response.body.data).toHaveProperty('email');
				expect(response.body.data).toHaveProperty('isActive');
				expect(response.body.data).toHaveProperty('clientOrigin');
			}
		});
	});

	describe('Input Validation Tests', () => {
		it('Should validate email format in all operations', async () => {
			const invalidEmails = [
				'invalid-email',
				'@example.com',
				'user@',
				'<EMAIL>',
				''
			];

			for (const email of invalidEmails) {
				const response = await request
					.get(`/api/v1/identity/email/${email}`)
					.set('Authorization', `Bearer ${adminAccessToken}`);

				expect(response.status).toBe(400);
			}
		});

		it('Should handle special characters in email safely', async () => {
			const specialEmail = '<EMAIL>';
			
			const response = await request
				.get(`/api/v1/identity/email/${encodeURIComponent(specialEmail)}`)
				.set('Authorization', `Bearer ${adminAccessToken}`);

			// Should handle properly encoded emails
			expect([200, 404]).toContain(response.status);
		});
	});

	describe('Error Handling Tests', () => {
		it('Should handle database connection errors gracefully', async () => {
			// This test would require mocking database failures
			// For now, just ensure the endpoint responds appropriately
			const response = await request
				.get(`/api/v1/identity/email/${managedUser.email}`)
				.set('Authorization', `Bearer ${adminAccessToken}`);

			expect(response.status).toBeLessThan(600); // Should not crash
		});

		it('Should provide meaningful error messages', async () => {
			const response = await request
				.get('/api/v1/identity/email/invalid-email')
				.set('Authorization', `Bearer ${adminAccessToken}`);

			expect(response.status).toBe(400);
			expect(response.body).toHaveProperty('error');
			expect(response.body.error).toHaveProperty('message');
		});
	});
});
