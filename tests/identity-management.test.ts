import { managedUser, regularUser, adminUser } from '@/constants/test-user.constants';
import { EnumClientOrigin, HttpStatus } from 'excelytics.shared-internals';
import { beforeAll, describe, afterAll, expect, it } from 'bun:test';
import { AuthTestHelper } from '@/helpers/auth.test-helper';
import supertest from 'supertest';
import mongoose from 'mongoose';
import app from '@/server';

const request = supertest(app);

let adminAccessToken: string;
let regularAccessToken: string;
let testUsersCreated: string[] = [];

// Helper function to get fresh admin token
async function getFreshAdminToken(): Promise<string> {
	await AuthTestHelper.setupAdminUser();
	// Access the admin token using the public getter method
	const adminToken = AuthTestHelper.getAdminToken();
	if (!adminToken) {
		throw new Error('Failed to get admin token');
	}
	return adminToken;
}

describe('Identity Management Tests', () => {
	beforeAll(async () => {
		console.log('🔧 Setting up Identity Management Tests...');

		// Setup admin user with proper permissions
		await AuthTestHelper.setupAdminUser();
		const adminToken = AuthTestHelper.getAdminToken();
		if (!adminToken) {
			throw new Error('Failed to setup admin user');
		}
		adminAccessToken = adminToken;

		// Register regular user and get token
		const regularTokens = await AuthTestHelper.registerAndLogin(regularUser);

		if (regularTokens) {
			regularAccessToken = regularTokens.accessToken;
			AuthTestHelper.trackUserForCleanup(regularUser.email);
		} else {
			throw new Error('Failed to register regular user');
		}

		// Register managed user (no token needed, just for testing operations)
		const managedTokens = await AuthTestHelper.registerAndLogin(managedUser);
		if (managedTokens) {
			AuthTestHelper.trackUserForCleanup(managedUser.email);
		}

		console.log('✅ Identity Management Tests setup complete');
	});

	afterAll(async () => {
		console.log('🧹 Cleaning up Identity Management Tests...');

		// Clean up all test users
		await AuthTestHelper.cleanupAllUsers();

		console.log('✅ Identity Management Tests cleanup complete');
	});

	describe('Authentication Required Tests', () => {
		it('Should reject unauthenticated requests to identity endpoints', async () => {
			const response = await request
				.get(`/api/v1/identity/user/${managedUser.email}`);

			expect(response.status).toBe(401);
		});

		it('Should reject requests with invalid tokens', async () => {
			const response = await request
				.get(`/api/v1/identity/user/${managedUser.email}`)
				.set('Authorization', 'Bearer invalid.token.here');

			expect(response.status).toBe(401);
		});
	});

	describe('Admin Role Required Tests', () => {
		it('Should reject regular user access to identity management', async () => {
			const response = await request
				.get(`/api/v1/identity/user/${managedUser.email}`)
				.set('Authorization', `Bearer ${regularAccessToken}`);

			// Should be 403 (Forbidden) due to admin role requirement
			expect(response.status).toBe(403);
		});

		it('Should allow admin user access to identity management', async () => {
			const freshAdminToken = await getFreshAdminToken();
			const response = await request
				.get(`/api/v1/identity/user/${managedUser.email}`)
				.set('Authorization', `Bearer ${freshAdminToken}`);

			// Admin should have access - expect 200 (found) or 404 (not found), but not 403 (forbidden)
			expect([200, 404]).toContain(response.status);
			expect(response.status).not.toBe(403);
		});
	});

	describe('User Lookup Tests', () => {
		it('Should find existing user by email', async () => {
			const freshAdminToken = await getFreshAdminToken();
			const response = await request
				.get(`/api/v1/identity/user/${managedUser.email}`)
				.set('Authorization', `Bearer ${freshAdminToken}`);

			if (response.status === 200) {
				expect(response.body).toHaveProperty('success', true);
				expect(response.body.data).toHaveProperty('email', managedUser.email);
				expect(response.body.data).not.toHaveProperty('password'); // Should not expose password
			}
		});

		it('Should return 404 for non-existent user', async () => {
			const freshAdminToken = await getFreshAdminToken();
			const response = await request
				.get('/api/v1/identity/user/<EMAIL>')
				.set('Authorization', `Bearer ${freshAdminToken}`);

			// Admin should have access, so expect 404 for non-existent user, not 403
			expect(response.status).toBe(404);
			expect(response.status).not.toBe(403);
		});

		it('Should validate email format in lookup', async () => {
			const freshAdminToken = await getFreshAdminToken();
			const response = await request
				.get('/api/v1/identity/user/invalid-email-format')
				.set('Authorization', `Bearer ${freshAdminToken}`);

			// Should validate email format and return 400, not 403 (admin has access)
			expect(response.status).toBe(400);
			expect(response.status).not.toBe(403);
		});
	});

	describe('User Management Operations', () => {
		it('Should allow admin to update user status', async () => {
			// This test assumes there's an endpoint to update user status
			const updateData = { isActive: false };

			const freshAdminToken = await getFreshAdminToken();
			const response = await request
				.put(`/api/v1/identity/user/${managedUser.email}`)
				.set('Authorization', `Bearer ${freshAdminToken}`)
				.send(updateData);

			// Admin should have access - expect success, not found, or not implemented, but not forbidden
			expect([200, 404, 501]).toContain(response.status);
			expect(response.status).not.toBe(403);
		});

		it('Should allow admin to delete user', async () => {
			// Create a temporary user for deletion test with unique email
			const tempUser = {
				clientId: new mongoose.Types.ObjectId(),
				clientOrigin: EnumClientOrigin.Excelytics,
				email: `temp-delete-${Date.now()}@example.com`,
				password: 'TempDelete@123',
				isActive: true
			};

			await request.post('/api/v1/auth/register').send(tempUser);
			AuthTestHelper.trackUserForCleanup(tempUser.email); // Track for cleanup

			const freshAdminToken = await getFreshAdminToken();
			const response = await request
				.delete(`/api/v1/identity/email/${tempUser.email}`)
				.set('Authorization', `Bearer ${freshAdminToken}`);

			// Admin should have access to delete - expect success, but not forbidden
			expect([200, 204]).toContain(response.status);
			expect(response.status).not.toBe(403);
		});

		it('Should prevent regular users from deleting accounts', async () => {
			const response = await request
				.delete(`/api/v1/identity/email/${managedUser.email}`)
				.set('Authorization', `Bearer ${regularAccessToken}`);

			expect(response.status).toBe(403);
		});
	});

	describe('User Data Security Tests', () => {
		it('Should not expose sensitive user data', async () => {
			const response = await request
				.get(`/api/v1/identity/user/${managedUser.email}`)
				.set('Authorization', `Bearer ${adminAccessToken}`);

			if (response.status === 200) {
				expect(response.body.data).not.toHaveProperty('password');
				expect(response.body.data).not.toHaveProperty('passwordHash');
				expect(response.body.data).not.toHaveProperty('salt');
			}
		});

		it('Should include only necessary user information', async () => {
			const response = await request
				.get(`/api/v1/identity/user/${managedUser.email}`)
				.set('Authorization', `Bearer ${adminAccessToken}`);

			if (response.status === 200) {
				expect(response.body.data).toHaveProperty('email');
				expect(response.body.data).toHaveProperty('isActive');
				expect(response.body.data).toHaveProperty('clientOrigin');
			}
		});
	});

	describe('Input Validation Tests', () => {
		it('Should validate email format in all operations', async () => {
			const invalidEmails = [
				'invalid-email',
				'@example.com',
				'user@',
				'<EMAIL>'
				// Note: Empty string '' is excluded as it creates malformed URL
			];

			const freshAdminToken = await getFreshAdminToken();
			for (const email of invalidEmails) {
				const response = await request
					.get(`/api/v1/identity/user/${email}`)
					.set('Authorization', `Bearer ${freshAdminToken}`);

				// Admin has access, so should get validation error (400), not forbidden (403)
				expect(response.status).toBe(400);
				expect(response.status).not.toBe(403);
			}
		});

		it('Should handle special characters in email safely', async () => {
			const specialEmail = '<EMAIL>';

			const freshAdminToken = await getFreshAdminToken();
			const response = await request
				.get(`/api/v1/identity/user/${encodeURIComponent(specialEmail)}`)
				.set('Authorization', `Bearer ${freshAdminToken}`);

			// Admin has access, should handle properly encoded emails
			expect([200, 404]).toContain(response.status);
			expect(response.status).not.toBe(403);
		});
	});

	describe('Error Handling Tests', () => {
		it('Should handle database connection errors gracefully', async () => {
			// This test would require mocking database failures
			// For now, just ensure the endpoint responds appropriately
			const freshAdminToken = await getFreshAdminToken();
			const response = await request
				.get(`/api/v1/identity/user/${managedUser.email}`)
				.set('Authorization', `Bearer ${freshAdminToken}`);

			expect(response.status).toBeLessThan(600); // Should not crash
		});

		it('Should provide meaningful error messages', async () => {
			const freshAdminToken = await getFreshAdminToken();
			const response = await request
				.get('/api/v1/identity/user/invalid-email')
				.set('Authorization', `Bearer ${freshAdminToken}`);

			// Admin has access, should get validation error with meaningful message
			expect(response.status).toBe(400);
			expect(response.status).not.toBe(403);
			expect(response.body).toHaveProperty('error');
			expect(response.body.error).toHaveProperty('message');
		});
	});
});