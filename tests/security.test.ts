import { TestDataFactory, TestAssertions, TestUtils } from './test-runner';
import { TokenAnalysisHelper } from './helpers/token-analysis-helper';
import { beforeAll, describe, afterAll, expect, it } from 'bun:test';
import type { AuthTokens, TestUser } from './constants/test.types';
import { EnumClientOrigin } from 'excelytics.shared-internals';
import AuthTestHelper from './helpers/auth.test-helper';
import supertest from 'supertest';
import mongoose from 'mongoose';
import app from '@/server';

const request = supertest(app);

// Test user for authentication tests
let securityTestUser: TestUser;
let authTokens: AuthTokens | null = null;

describe.skip('[REJECTIONS] Authentication Middleware Tests', () => {
	it('Should reject requests without Authorization header', async () => {
		const response = await request.get(`/api/v1/identity/email/${securityTestUser.email}`);

		expect(response.status).toBe(401);
	});

	it('Should reject requests with invalid Bearer token format', async () => {
		const response = await request
			.get(`/api/v1/identity/email/${securityTestUser.email}`)
			.set('Authorization', 'InvalidFormat token');

		expect(response.status).toBe(401);
	});

	it('Should reject requests with malformed JWT token', async () => {
		const malformedToken = AuthTestHelper.generateMalformedToken();
		const response = await request
			.get(`/api/v1/identity/email/${securityTestUser.email}`)
			.set('Authorization', `Bearer ${malformedToken}`);

		expect(response.status).toBe(401);
	});

	it('Should reject expired tokens', async () => {
		const expiredToken = await AuthTestHelper.generateExpiredToken();

		const response = await request
			.get(`/api/v1/identity/email/${securityTestUser.email}`)
			.set('Authorization', `Bearer ${expiredToken}`);

		expect(response.status).toBe(401);
	});
});

describe('[ACCEPTANCE] Authentication Middleware Tests', () => {
	beforeAll(async () => {
		// Setup admin user for cleanup operations
		await AuthTestHelper.setupAdminUser();

		// Create a unique test user for this test suite
		securityTestUser = AuthTestHelper.createTestUser('security', true);

		// Register user to acquire authentication tokens
		authTokens = await AuthTestHelper.registerAndLogin(securityTestUser);
		if (!authTokens) {
			console.warn('❌ Failed to create test user for security tests');
		} else {
			console.log('✅ Security test user created successfully');
		}
	});

	afterAll(async () => {
		console.log('🧹 Cleaning up all test users with admin privileges...');

		await AuthTestHelper.cleanupAllUsers();
		console.log('🏁 Security tests completed');
	});

	it('Should accept requests with valid Bearer token', async () => {
		// Skip this test if we don't have a valid token
		if (!authTokens?.accessToken) {
			console.warn('Skipping token test - no valid access token available');
			return;
		}

		console.log('🔍 Testing authentication with valid token...');

		// Test with an endpoint that doesn't require admin permissions
		const healthResponse = await request
			.get('/api/v1/health')
			.set('Authorization', `Bearer ${authTokens.accessToken}`);

		console.log('✅ Health endpoint response:', healthResponse.status);

		// Health endpoint should work with any valid token
		expect(healthResponse.status).toBe(200);

		// Test with admin-required endpoint (should return 403, not 401)
		const adminResponse = await request
			.get(`/api/v1/identity/email/${securityTestUser.email}`)
			.set('Authorization', `Bearer ${authTokens.accessToken}`);

		console.log('🔒 Admin endpoint response:', adminResponse.status);
		console.log('📄 Admin endpoint message:', adminResponse.body.message);

		// Should not be 401 (unauthorized) - should be 403 (forbidden) for regular users
		expect(adminResponse.status).not.toBe(401);
		expect(adminResponse.status).toBe(403); // Regular user accessing admin endpoint
	});
});

describe.skip('Security and Middleware Tests', () => {
	beforeAll(async () => {
		console.log('🚀 Starting security tests...');

		// Setup admin user for cleanup operations
		console.log('👑 Setting up admin user for cleanup...');
		await AuthTestHelper.setupAdminUser();

		// Create a unique test user for this test suite
		securityTestUser = AuthTestHelper.createTestUser('security');

		console.log('🔐 Creating security test user:', {
			email: securityTestUser.email,
			clientId: securityTestUser.clientId.toString(),
			clientOrigin: securityTestUser.clientOrigin
		});

		// Register and get authentication tokens
		authTokens = await AuthTestHelper.registerAndLogin(securityTestUser);

		if (!authTokens) {
			console.warn('❌ Failed to create test user for security tests');
		} else {
			console.log('✅ Security test user created successfully');
			// Token analysis is now handled in the AuthTestHelper.registerAndLogin method
		}
	});

	afterAll(async () => {
		console.log('🧹 Cleaning up all test users with admin privileges...');
		// Clean up all created users using admin privileges
		await AuthTestHelper.cleanupAllUsers();
		console.log('🏁 Security tests completed');
	});

	describe('Authentication Middleware Tests', () => {
		it('Should validate token using verify endpoint', async () => {
			// Skip this test if we don't have a valid token
			if (!authTokens?.accessToken) {
				console.warn('Skipping token verification test - no valid access token available');
				return;
			}

			console.log('🔍 Verifying token with IdP...');
			console.log('📤 Sending token:', authTokens.accessToken.substring(0, 50) + '...');

			const response = await request
				.post('/api/v1/verify-access-token')
				.send({ token: authTokens.accessToken });

			TokenAnalysisHelper.logTokenVerification(response, 'IdP Verification');

			expect(response.status).toBe(200);
			expect(response.body.success).toBe(true);
			expect(response.body.data.active).toBe(true);
		});

		it('Should use token for authenticated operations', async () => {
			// Skip this test if we don't have a valid token
			if (!authTokens?.accessToken) {
				console.warn(
					'Skipping authenticated operations test - no valid access token available'
				);
				return;
			}

			console.log('🔐 Testing authenticated operations...');

			// Test accessing user's own data
			console.log('📋 Testing user data access...');
			const userDataResponse = await request
				.get(`/api/v1/identity/email/${securityTestUser.email}`)
				.set('Authorization', `Bearer ${authTokens.accessToken}`);

			TokenAnalysisHelper.logUserDataAccess(userDataResponse);

			// Test other endpoints with authentication
			const endpoints = [
				{ path: '/api/v1/health', description: 'Health Check' },
				{ path: '/api/v1/auth/session', description: 'Session Info' }
			];

			for (const endpoint of endpoints) {
				const response = await request
					.get(endpoint.path)
					.set('Authorization', `Bearer ${authTokens.accessToken}`);

				TokenAnalysisHelper.logHealthCheck(response, endpoint.path);

				// These endpoints should work with valid tokens (200) or might not require auth
				expect([200, 404]).toContain(response.status);
			}

			// Should not be 401 for any authenticated request
			expect(userDataResponse.status).not.toBe(401);
		});
	});

	describe('Authenticated Operations Tests', () => {
		it('Should perform authenticated user operations', async () => {
			// Create a fresh user for authenticated operations
			const testUser = AuthTestHelper.createTestUser('auth-ops');
			const tokens = await AuthTestHelper.registerAndLogin(testUser);

			if (!tokens) {
				throw new Error('Failed to create user for authenticated operations test');
			}

			console.log('Created user for auth ops:', testUser.email);
			console.log('Got tokens:', {
				accessToken: tokens.accessToken.substring(0, 50) + '...',
				hasRefreshToken: !!tokens.refreshToken
			});

			// Test 1: Verify the token works
			const verifyResponse = await request
				.post('/api/v1/verify-access-token')
				.send({ token: tokens.accessToken });

			expect(verifyResponse.status).toBe(200);
			expect(verifyResponse.body.success).toBe(true);
			expect(verifyResponse.body.data.active).toBe(true);

			// Test 2: Try to access user's own data (should work or give 403 for role restrictions)
			const userDataResponse = await request
				.get(`/api/v1/identity/email/${testUser.email}`)
				.set('Authorization', `Bearer ${tokens.accessToken}`);

			console.log(
				'User data access response:',
				userDataResponse.status,
				userDataResponse.body
			);

			// Should not be 401 (unauthorized) - either 200 (success), 403 (forbidden due to roles), or 404 (not found)
			expect(userDataResponse.status).not.toBe(401);

			// Test 3: Try authenticated endpoints
			const healthResponse = await request
				.get('/api/v1/health')
				.set('Authorization', `Bearer ${tokens.accessToken}`);

			expect(healthResponse.status).toBe(200);

			// Note: Cleanup handled by admin user in afterAll
		});

		it('Should handle token refresh operations', async () => {
			// Create a user for refresh token testing
			const testUser = AuthTestHelper.createTestUser('refresh-test');
			const tokens = await AuthTestHelper.registerAndLogin(testUser);

			if (!tokens || !tokens.refreshToken) {
				console.warn('Skipping refresh token test - no refresh token available');
				return;
			}

			console.log('Testing refresh token functionality');

			// Verify both tokens work
			const accessTokenVerify = await request
				.post('/api/v1/verify-access-token')
				.send({ token: tokens.accessToken });

			expect(accessTokenVerify.status).toBe(200);
			expect(accessTokenVerify.body.data.active).toBe(true);

			// Note: Cleanup handled by admin user in afterAll
		});
	});

	describe('Token Analysis and Admin User Tests', () => {
		it('Should create user and analyze complete token structure', async () => {
			console.log('\n🔬 === COMPREHENSIVE TOKEN ANALYSIS ===');

			// Create a fresh user for detailed analysis
			const analysisUser = AuthTestHelper.createTestUser('token-analysis');
			console.log('🆕 Creating analysis user:', analysisUser.email);

			const tokens = await AuthTestHelper.registerAndLogin(analysisUser);

			if (!tokens) {
				throw new Error('Failed to create user for token analysis');
			}

			// Test token verification
			const verifyResponse = await request
				.post('/api/v1/verify-access-token')
				.send({ token: tokens.accessToken });

			TokenAnalysisHelper.logTokenVerification(verifyResponse);

			// Test authenticated request
			const authResponse = await request
				.get(`/api/v1/identity/email/${analysisUser.email}`)
				.set('Authorization', `Bearer ${tokens.accessToken}`);

			TokenAnalysisHelper.logAuthenticatedRequest(authResponse);

			// Assertions
			expect(tokens.accessToken).toBeDefined();
			expect(tokens.refreshToken).toBeDefined();
			expect(verifyResponse.status).toBe(200);
			expect(verifyResponse.body.data.active).toBe(true);

			// Note: Cleanup handled by admin user in afterAll
			console.log('🧹 Analysis user will be cleaned up by admin\n');
		});

		it('Should create admin user and test elevated permissions', async () => {
			console.log('\n👑 === ADMIN USER TESTING ===');

			// Use the existing admin user setup
			const adminReady = await AuthTestHelper.setupAdminUser();
			expect(adminReady).toBe(true);

			// Get admin user info (this is a bit of a hack to access private property)
			const adminUser = (AuthTestHelper as any).adminUser;
			if (!adminUser?.accessToken) {
				throw new Error('Failed to get admin user token');
			}

			const adminTokens: AuthTokens = {
				accessToken: adminUser.accessToken,
				refreshToken: '', // Not needed for this test
				user: {
					id: adminUser.clientId,
					email: adminUser.email,
					clientId: adminUser.clientId,
					clientOrigin: adminUser.clientOrigin,
					isActive: true,
					roles: ['user', 'admin']
				}
			};

			// Demonstrate comprehensive token analysis
			TokenAnalysisHelper.comprehensiveTokenDemo(adminTokens, 'Admin User Testing');

			// Test Request.user analysis simulation
			TokenAnalysisHelper.demonstrateRequestUserFunctionality(
				adminTokens.accessToken,
				'Admin User Testing'
			);

			// Create a regular user to test deletion
			const regularUser = AuthTestHelper.createTestUser('to-be-deleted');
			const regularTokens = await AuthTestHelper.registerAndLogin(regularUser);

			if (!regularTokens) {
				throw new Error('Failed to create regular user for deletion test');
			}

			console.log('🎯 Testing admin deletion capabilities...');

			// Test admin deletion (this might fail if permissions aren't implemented yet)
			const deletionSuccess = await AuthTestHelper.deleteUserAsAdmin(
				adminTokens.accessToken,
				regularUser.email
			);

			console.log(
				'🗑️ Admin deletion result:',
				deletionSuccess
					? '✅ Success'
					: '⚠️ Failed (expected if permissions not implemented)'
			);

			// Note: Cleanup handled by admin user in afterAll

			// Assertions
			expect(adminTokens.accessToken).toBeDefined();
			expect(adminTokens.refreshToken).toBeDefined();
			// Note: Deletion might fail if admin permissions aren't implemented yet
		});

		it('Should demonstrate Express Request.user functionality', async () => {
			console.log('\n🔧 === EXPRESS REQUEST.USER DEMONSTRATION ===');

			// Create a test user
			const testUser = AuthTestHelper.createTestUser('express-demo');
			const tokens = await AuthTestHelper.registerAndLogin(testUser);

			if (!tokens) {
				throw new Error('Failed to create user for Express demo');
			}

			// Comprehensive token demonstration
			TokenAnalysisHelper.comprehensiveTokenDemo(tokens, 'Express Integration');

			// Demonstrate Request.user functionality
			TokenAnalysisHelper.demonstrateRequestUserFunctionality(tokens.accessToken);

			// Test with health endpoint (should work)
			console.log('\n🔍 === TESTING REQUEST.USER ANALYSIS ===');
			console.log('📝 Raw Token Analysis:');

			const healthResponse = await request
				.get('/api/v1/health')
				.set('Authorization', `Bearer ${tokens.accessToken}`);

			TokenAnalysisHelper.logHealthCheck(healthResponse, '/api/v1/health');

			// Note: Cleanup handled by admin user in afterAll

			// Assertions
			expect(tokens.accessToken).toBeDefined();
			expect(tokens.refreshToken).toBeDefined();
		});
	});

	describe('Rate Limiting Tests', () => {
		it('Should apply rate limiting to login failures', async () => {
			const invalidCredentials = {
				email: securityTestUser.email,
				password: 'WrongPassword@123'
			};

			// Use the helper to test rate limiting
			const statusCodes = await AuthTestHelper.testRateLimit(
				'/api/v1/auth/login',
				invalidCredentials,
				12
			);

			// At least one should be rate limited (429)
			const rateLimitedResponses = statusCodes.filter(status => status === 429);
			expect(rateLimitedResponses.length).toBeGreaterThan(0);
		});

		it('Should include rate limit headers', async () => {
			const response = await request.get('/api/v1/health');

			// Check for rate limit headers - express-rate-limit uses different header names
			// The actual header names depend on the configuration in rate-limit.middleware.ts
			// Let's check for any rate limit related headers
			const hasRateLimitHeaders =
				response.header['x-ratelimit-limit'] ||
				response.header['x-ratelimit-remaining'] ||
				response.header['x-ratelimit-reset'] ||
				response.header['ratelimit-limit'] ||
				response.header['ratelimit-remaining'];

			// Rate limit headers might not be present on successful requests
			// This test verifies the endpoint responds successfully
			expect(response.status).toBe(200);
		});

		it('Should reset rate limit after time window', async () => {
			// This test would require waiting for rate limit window to reset
			// For now, just verify the endpoint responds normally after some time
			await new Promise(resolve => setTimeout(resolve, 1000));

			const response = await request.get('/api/v1/health');
			expect(response.status).toBe(200);
		});
	});

	describe('CORS Security Tests', () => {
		it('Should allow requests from allowed origins', async () => {
			// Use test-runner to get proper allowed origins
			const corsTestData = TestDataFactory.createCORSTestData();
			const allowedOrigin = corsTestData.allowedOrigins[0]; // Use first allowed origin

			const response = await request.get('/api/v1/health').set('Origin', allowedOrigin);

			expect(response.status).toBe(200);

			// Use test-runner assertion for CORS validation
			TestAssertions.assertCORSHeaders(response, allowedOrigin);
		});

		it('Should block requests from disallowed origins', async () => {
			// Use test-runner to get proper disallowed origins
			const corsTestData = TestDataFactory.createCORSTestData();
			const disallowedOrigin = corsTestData.disallowedOrigins[0]; // Use first disallowed origin

			const response = await request.get('/api/v1/health').set('Origin', disallowedOrigin);

			// CORS errors are handled by the global error handler and return 500
			// The important thing is that the request is blocked
			expect(response.status).toBe(500);
			expect(response.body.message).toContain('unexpected error');
		});

		it('Should handle preflight OPTIONS requests', async () => {
			const response = await request
				.options('/api/v1/auth/login')
				.set('Origin', 'http://localhost:4200') // Use allowed origin
				.set('Access-Control-Request-Method', 'POST')
				.set('Access-Control-Request-Headers', 'Content-Type');

			// CORS configuration returns 200 for OPTIONS requests, not 204
			expect(response.status).toBe(200);
			expect(response.header['access-control-allow-methods']).toContain('POST');
		});
	});

	describe('Helmet Security Headers Tests', () => {
		it('Should include X-Frame-Options header', async () => {
			const response = await request.get('/api/v1/health');

			expect(response.status).toBe(200);
			expect(response.header['x-frame-options']).toBe('DENY');
		});

		it('Should include X-Content-Type-Options header', async () => {
			const response = await request.get('/api/v1/health');

			expect(response.status).toBe(200);
			expect(response.header['x-content-type-options']).toBe('nosniff');
		});

		it('Should include X-XSS-Protection header', async () => {
			const response = await request.get('/api/v1/health');

			expect(response.status).toBe(200);
			expect(response.header['x-xss-protection']).toBe('0');
		});

		it('Should include X-Permitted-Cross-Domain-Policies header', async () => {
			const response = await request.get('/api/v1/health');

			expect(response.status).toBe(200);
			expect(response.header['x-permitted-cross-domain-policies']).toBe('none');
		});

		it('Should not expose server information', async () => {
			const response = await request.get('/api/v1/health');

			expect(response.status).toBe(200);
			expect(response.header['server']).toBeUndefined();
			expect(response.header['x-powered-by']).toBeUndefined();
		});
	});

	describe('Input Validation and Sanitization', () => {
		it('Should reject invalid registration data using TestDataFactory', async () => {
			// Use test-runner factory to generate invalid test data
			const invalidDataSets = TestDataFactory.createInvalidRegistrationData();

			for (const invalidData of invalidDataSets) {
				const response = await request.post('/api/v1/auth/register').send(invalidData);

				// Should reject with validation error or rate limiting
				expect([400, 422, 429]).toContain(response.status);

				if (response.status === 429) {
					console.log(
						'⚠️ Security validation test hit rate limit - this is expected behavior'
					);
				}
				TestAssertions.assertErrorResponse(response, response.status);

				// Wait to avoid rate limiting
				await TestUtils.wait(500);
			}
		});

		it('Should reject oversized JSON payloads', async () => {
			const largePayload = {
				email: '<EMAIL>',
				password: 'a'.repeat(50 * 1024 * 1024), // 50MB string
				data: 'x'.repeat(50 * 1024 * 1024)
			};

			const response = await request.post('/api/v1/auth/register').send(largePayload);

			expect(response.status).toBe(413); // Payload Too Large
		});

		it('Should sanitize and validate email inputs', async () => {
			// Wait to avoid rate limiting
			await AuthTestHelper.wait(2000);

			const maliciousPayload = {
				email: '<script>alert("xss")</script>@example.com',
				password: 'Test@123',
				clientId: new mongoose.Types.ObjectId(),
				clientOrigin: EnumClientOrigin.Excelytics,
				isActive: true
			};

			const response = await request.post('/api/v1/auth/register').send(maliciousPayload);

			// Should reject invalid email with 400, but might get 429 due to rate limiting
			expect([400, 429]).toContain(response.status);
		});

		it('Should handle SQL injection attempts gracefully', async () => {
			// Wait to avoid rate limiting
			await AuthTestHelper.wait(2000);

			const sqlInjectionPayload = {
				email: "'; DROP TABLE users; --",
				password: 'Test@123'
			};

			const response = await request.post('/api/v1/auth/login').send(sqlInjectionPayload);

			// Should reject invalid input with 400, but might get 429 due to rate limiting
			expect([400, 429]).toContain(response.status);
		});
	});
});