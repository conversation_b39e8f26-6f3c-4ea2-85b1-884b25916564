import { EnumClientPath, MongooseStates, HealthStatus, HttpStatus } from 'excelytics.shared-internals';
import { beforeAll, describe, afterAll, expect, it } from 'bun:test';
import type { HealthResponseType } from '@/constants/test.types';
import { TEST_CONFIG } from '@/constants/test-config.constants';
import { TestAssertions, TestUtils } from './test-runner';
import supertest from 'supertest';
import mongoose from 'mongoose';
import app from '@/server';

const request = supertest(app);

describe('Health Check Tests', () => {
	beforeAll(async () => {
		// Ensure database connection is established
		if (mongoose.connection.readyState !== MongooseStates.CONNECTED) {
			console.log('Waiting for database connection...');
			await new Promise(resolve => {
				mongoose.connection.once('connected', resolve);
			});
		}
	});

	afterAll(async () => {
		console.log('❤️‍🩹 Health check tests completed');
	});

	describe('Shallow Health Check', () => {
		it('Should return 200 for basic health check', async () => {
			const response = await request.get('/api/v1/health');

			expect(response.status).toBe(HttpStatus.OK);
			expect(response.body).toHaveProperty('success', true);
			expect(response.body).toHaveProperty('data');

			// Use test-runner utilities for validation
			expect(TestUtils.validateSuccessResponse(response.body)).toBe(true);

			expect(response.body.data).toHaveProperty('status', HealthStatus.HEALTHY);
			expect(response.body.data).toHaveProperty('service');
			expect(response.body.data).toHaveProperty('version');
			expect(response.body.data).toHaveProperty('timestamp');
		});

		it('Should include service metadata in health response', async () => {
			const response = await request.get('/api/v1/health');

			expect(response.status).toBe(HttpStatus.OK);
			expect(response.body.data.service).toBe(EnumClientPath.Identity);
			expect(response.body.data).toHaveProperty('responseTime');
			expect(typeof response.body.data.responseTime).toBe('number');
			expect(response.body.data).toHaveProperty('dependencies');
			expect(Array.isArray(response.body.data.dependencies)).toBe(true);
		});

		it('Should respond quickly (under 1 second)', async () => {
			const startTime = Date.now();
			const response = await request.get('/api/v1/health');
			const endTime = Date.now();

			expect(response.status).toBe(HttpStatus.OK);

			// Use test-runner utility for performance validation, ensures response is not greater than 1000ms
			TestAssertions.assertResponseTime(
				startTime,
				endTime,
				TEST_CONFIG.PERFORMANCE_THRESHOLDS.HEALTH_CHECK_MAX_TIME
			);
		});
	});

	describe('Deep Health Check', () => {
		it('Should return correct response body structure', async () => {
			const response = await request.get('/api/v1/health/all');

			try {
				// Ensure the response body matches the structure we expect
				const body: HealthResponseType = response.body;
				expect(body).toBeDefined();
			} catch (error) {
				console.error('Failed to parse response body:', error);
				expect(false).toBe(true); // Fail the test
			}
		});

		it('Should handle unhealthy services gracefully', async () => {
			const response = await request.get('/api/v1/health/all');

			const responseBody: HealthResponseType = response.body;
			const unhealthyServices = responseBody.data.dependencies.filter((_) => _.status === HealthStatus.UNHEALTHY);

			if (unhealthyServices.length > 0) {
				console.log(`⛓️‍💥 Unhealthy services found (${unhealthyServices.length}):`, unhealthyServices);
				expect(response.status).toBe(HttpStatus.SERVICE_UNAVAILABLE);
				expect(responseBody.success).toBe(false);
			} else {
				console.log('🏥 All services are healthy');
				expect(response.status).toBe(HttpStatus.OK);
				expect(responseBody.success).toBe(true);
			}
		});

		it('Should handle comprehensive health check (returns 503 for unavailable services)', async () => {
			const startTime = Date.now();
			const response = await request.get('/api/v1/health/all');
			const endTime = Date.now();

			const responseBody: HealthResponseType = response.body;
			const unhealthyServices = responseBody.data.dependencies.filter((_) => _.status === HealthStatus.UNHEALTHY);
			const healthyServices = responseBody.data.dependencies.filter((_) => _.status === HealthStatus.HEALTHY);

			if (unhealthyServices.length > 0) {
				console.log(`⛓️‍💥 Unhealthy services found (${unhealthyServices.length}):`);
				unhealthyServices.forEach((service) => {
					console.log(`- ${service.name}: ${service.error}`);
				});

				expect(response.status).toBe(HttpStatus.SERVICE_UNAVAILABLE);
				expect(responseBody.success).toBe(false);
			}

			if (healthyServices.length > 0) {
				console.log(`\n️‍💚 Healthy services found (${healthyServices.length}):`);
				healthyServices.forEach((service) => {
					console.log(`- ${service.name}, response time: ${service.responseTime}`);

					expect(service.status).toBe(HealthStatus.HEALTHY);
				});
			}

			// Structure assertions
			expect(response.body).toHaveProperty('success');
			expect(response.body).toHaveProperty('data');
			expect(response.body.data).toHaveProperty('dependencies');
			expect(Array.isArray(response.body.data.dependencies)).toBe(true);

			// Check deep health check performance
			TestAssertions.assertResponseTime(
				startTime,
				endTime,
				TEST_CONFIG.PERFORMANCE_THRESHOLDS.DEEP_HEALTH_CHECK_MAX_TIME
			);
		});

		it('Should include MongoDB health check even when external services are down', async () => {
			const response = await request.get('/api/v1/health/all');

			// Accept both 200 and 503 status codes
			expect([200, 503]).toContain(response.status);
			expect(response.body.data.dependencies).toBeInstanceOf(Array);

			const mongoCheck = response.body.data.dependencies.find(
				(check: any) => check.name === 'MongoDB'
			);

			expect(mongoCheck).toBeDefined();
			expect(mongoCheck.status).toBe('healthy');
			expect(mongoCheck).toHaveProperty('responseTime');
			expect(typeof mongoCheck.responseTime).toBe('number');
		});

		it('Should include all required health check properties', async () => {
			const response = await request.get('/api/v1/health/all');

			// Accept both 200 and 503 status codes
			expect([200, 503]).toContain(response.status);
			expect(response.body.data).toHaveProperty('service', EnumClientPath.Identity);
			expect(response.body.data).toHaveProperty('version');
			expect(response.body.data).toHaveProperty('timestamp');
			expect(response.body.data).toHaveProperty('responseTime');
			expect(response.body.data).toHaveProperty('dependencies');
		});

		it('Should respond within reasonable time (under 5 seconds)', async () => {
			const startTime = Date.now();
			const response = await request.get('/api/v1/health/all');
			const endTime = Date.now();

			// Accept both 200 and 503 status codes
			expect([200, 503]).toContain(response.status);

			const responseTime = endTime - startTime;
			expect(responseTime).toBeLessThan(5000);

			console.log(`\n⏱️ Deep health check completed within ${responseTime}ms`);
		});
	});

	describe('Health Check Error Scenarios', () => {
		it('Should return 404 for invalid health endpoint', async () => {
			const response = await request.get('/api/v1/health/invalid');
			
			expect(response.status).toBe(HttpStatus.NOT_FOUND);
		});

		it('Should handle health check with query parameters gracefully', async () => {
			const response = await request.get('/api/v1/health?format=json&detailed=true');
			
			expect(response.status).toBe(HttpStatus.OK);
			expect(response.body).toHaveProperty('success', true);
		});
	});

	describe('Health Check Headers and Security', () => {
		it('Should include proper security headers', async () => {
			const response = await request.get('/api/v1/health');
			
			expect(response.status).toBe(HttpStatus.OK);
			expect(response.header['x-frame-options']).toBe('DENY');
			expect(response.header['x-content-type-options']).toBe('nosniff');
			expect(response.header['content-type']).toContain('application/json');
		});

		it('Should not expose sensitive information in health response', async () => {
			const response = await request.get('/api/v1/health/all');

			// Accept both 200 and 503 status codes
			expect([200, 503]).toContain(response.status);
			expect(response.body).not.toHaveProperty('database_url');
			expect(response.body).not.toHaveProperty('secrets');
			expect(response.body).not.toHaveProperty('environment_variables');
		});
	});
});