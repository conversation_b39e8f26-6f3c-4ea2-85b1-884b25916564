import {
	type RefreshTokenPayload,
	type AccessTokenPayload,
	type SuccessR<PERSON>ponse,
	type ErrorR<PERSON>ponse,
	type TokenResponse,
	EnumClientOrigin,
	type ApiResponse,
	EnumClientPath
} from 'excelytics.shared-internals';
import type { Response } from 'supertest';
import mongoose from 'mongoose';

/**
 * Test user interface for creating test users
 * Based on Identity model structure
 */
export interface TestUser {
	clientId: mongoose.Types.ObjectId;
	clientOrigin: EnumClientOrigin;
	clientPath?: EnumClientPath;
	email: string;
	password: string;
	isActive: boolean;
	roles?: string[];
}

/** Test user creation data (matches Identity model) */
export interface TestUserCreateData {
	clientId: string;
	clientOrigin: EnumClientOrigin;
	email: string;
	password: string;
	isActive: boolean;
	roles?: string[];
}

/**
 * Authentication tokens returned from auth operations
 * Uses proper SharedInternals types
 */
export interface AuthTokens {
	accessToken: string;
	refreshToken: string;
	user: TestUserData;
}

/**
 * User data returned from authentication operations
 * Based on Identity model with toJSON transform
 */
export interface TestUserData {
	id: string; // Transformed from _id by Identity model
	email: string;
	clientId: string;
	clientOrigin: EnumClientOrigin;
	isActive: boolean;
	roles: string[];
	lastLogin?: Date;
	createdAt?: Date;
	updatedAt?: Date;
}

/** Token verification result */
export interface TokenVerificationResult {
	isValid: boolean;
	payload?: AccessTokenPayload | RefreshTokenPayload;
	error?: string;
}

/** Authenticated request helper interface */
export interface AuthenticatedRequestHelper {
	get: (url: string) => Promise<Response>;
	post: (url: string) => Promise<Response>;
	put: (url: string) => Promise<Response>;
	delete: (url: string) => Promise<Response>;
	patch: (url: string) => Promise<Response>;
}

// --- UNUSED ---
/** Test response types using SharedInternals response types */
export type TestApiResponse<T = any> = ApiResponse<T>;
export type TestSuccessResponse<T = any> = SuccessResponse<T>;
export type TestErrorResponse = ErrorResponse;
export type TestTokenResponse = TokenResponse;

/** Test context for tracking test operations */
export interface TestContext {
	testName: string;
	createdUsers: string[];
	adminToken?: string;
	startTime: number;
}

/** Rate limiting test result */
export interface RateLimitTestResult {
	statusCodes: number[];
	rateLimitHit: boolean;
	firstRateLimitIndex: number;
	totalRequests: number;
}