import { EnumLogLevel } from 'excelytics.shared-internals';
import { env_idp } from '@app-types/env_';

/**
 * Enhanced logging functions for different log levels
 * All respect the TEST_VERBOSE_LOGGING flag
 */
export const testLogger = {
	/** Logs at the 'DEBUG' level */
	log: (...args: any[]) => {
		if (env_idp.TEST_LOG_LEVEL === EnumLogLevel.Debug) {
			console.log(...args);
		}
	},

	/** Logs at the 'INFO' level */
	info: (...args: any[]) => {
		if (env_idp.TEST_LOG_LEVEL === EnumLogLevel.Info) {
			console.info(...args);
		}
	},

	/** Logs at the 'WARN' level */
	warn: (...args: any[]) => {
		if (env_idp.TEST_LOG_LEVEL === EnumLogLevel.Warn) {
			console.warn(...args);
		}
	},

	/** Logs at the 'ERROR' level */
	error: (...args: any[]) => {
		// always log errors
		console.error(...args);
	},

	/** Logs at the 'DEBUG' level */
	debug: (...args: any[]) => {
		if (env_idp.TEST_LOG_LEVEL === EnumLogLevel.Debug) {
			console.debug(...args);
		}
	},

	table: (data: any) => {
		if (env_idp.TEST_VERBOSE_LOGGING) {
			console.table(data);
		}
	},
};
