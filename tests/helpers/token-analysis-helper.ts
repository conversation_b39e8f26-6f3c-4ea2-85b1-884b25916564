import { logTokenAnalysis } from 'excelytics.shared-internals';
import { analyzeToken } from '@/utils/token-analysis.utils';
import type { AuthTokens } from './test.types';
import { testLogger } from './test-logger';

/** Helper class for comprehensive token analysis and logging in tests */
export class TokenAnalysisHelper {
	/** Perform comprehensive token analysis for both access and refresh tokens */
	static analyzeTokenPair(tokens: AuthTokens, context: string = 'Token Pair'): void {
		testLogger.log(`\n🔍 === ${context.toUpperCase()} ANALYSIS ===`);

		if (tokens.accessToken) {
			logTokenAnalysis(tokens.accessToken, `Access Token from ${context}`);
		}
		if (tokens.refreshToken) {
			logTokenAnalysis(tokens.refreshToken, `Refresh Token from ${context}`);
		}

		this.compareTokens(tokens);
	}
    
	/** Compare access and refresh tokens side by side */
	static compareTokens(tokens: AuthTokens): void {
		if (!tokens.accessToken || !tokens.refreshToken) {
			testLogger.log('⚠️ Cannot compare tokens - missing access or refresh token');
			return;
		}

		testLogger.info('\n🔄 === COMPARING ACCESS vs REFRESH TOKENS ===');

		const accessAnalysis = analyzeToken(tokens.accessToken);
		const refreshAnalysis = analyzeToken(tokens.refreshToken);

		if (accessAnalysis.isValid && refreshAnalysis.isValid) {
			testLogger.info('\n🔄 === COMPARING ACCESS TOKEN vs REFRESH TOKEN ===');
			testLogger.info(`Validity: ✅ vs ✅`);
			testLogger.info(`User ID: ${accessAnalysis.payload?.userId} vs ${refreshAnalysis.payload?.userId}`);
			testLogger.info(`Email: ${accessAnalysis.payload?.email} vs ${refreshAnalysis.payload?.email}`);
			testLogger.info(`Token Type: ${accessAnalysis.payload?.tokenType} vs ${refreshAnalysis.payload?.tokenType}`);
			testLogger.info(`Remaining Time: ${this.formatTime(accessAnalysis.payload?.exp)} vs ${this.formatTime(refreshAnalysis.payload?.exp)}`);
			testLogger.info('=== END COMPARISON ===');

			// Detailed comparison
			const accessLifetime = this.getTokenLifetime(accessAnalysis.payload?.exp);
			const refreshLifetime = this.getTokenLifetime(refreshAnalysis.payload?.exp);

			testLogger.info('\n📊 Detailed Comparison:');
			testLogger.info(`   Access Token Lifetime: ${accessLifetime}`);
			testLogger.info(`   Refresh Token Lifetime: ${refreshLifetime}`);
			testLogger.info(`   Same User: ${accessAnalysis.payload?.userId === refreshAnalysis.payload?.userId ? '✅' : '❌'}`);
			testLogger.info(`   Same Client: ${accessAnalysis.payload?.clientId === refreshAnalysis.payload?.clientId ? '✅' : '❌'}`);
			testLogger.info('=== END TOKEN COMPARISON ===');
		}
	}
    
	/** Demonstrate Express Request.user functionality */
	static demonstrateRequestUserFunctionality(token: string, context: string = 'Express Integration'): void {
		testLogger.log(`\n📋 Token Analysis for ${context}:`);

		// Analyze the token
		logTokenAnalysis(token, 'Test Token');

		// Simulate what would be available in req.user
		const analysis = analyzeToken(token);
		if (analysis.isValid && analysis.payload) {
			testLogger.info('\n👤 Token would populate req.user with:');
			testLogger.info(`   userId: ${analysis.payload.userId}`);
			testLogger.info(`   email: ${analysis.payload.email}`);
			testLogger.info(`   clientId: ${analysis.payload.clientId}`);
			testLogger.info(`   clientOrigin: ${analysis.payload.clientOrigin}`);
			testLogger.info(`   tokenType: ${analysis.payload.tokenType}`);
			testLogger.info('=== END REQUEST.USER ANALYSIS ===');

			testLogger.info('\n💡 In your Express controllers, you can now use:');
			testLogger.info('   const userId = getUserId(req);');
			testLogger.info('   const email = getUserEmail(req);');
			testLogger.info('   const clientId = getClientId(req);');
			testLogger.info('   const hasAdmin = userHasPermission(req, "admin");');
		}
	}
    
	/** Comprehensive token demonstration showing all analysis features */
	static comprehensiveTokenDemo(tokens: AuthTokens, context: string = 'Comprehensive Demo'): void {
		testLogger.log(`\n🎯 === ${context.toUpperCase()} ===`);

		// Individual token analysis
		if (tokens.accessToken) {
			logTokenAnalysis(tokens.accessToken, 'Access Token');
		}
		if (tokens.refreshToken) {
			logTokenAnalysis(tokens.refreshToken, 'Refresh Token');
		}

		// Token comparison
		this.compareTokens(tokens);

		// Extract user info
		if (tokens.accessToken) {
			const analysis = analyzeToken(tokens.accessToken);
			if (analysis.isValid && analysis.payload) {
				testLogger.info('\n👤 Extracted User Info:', {
					userId: analysis.payload.userId,
					email: analysis.payload.email,
					clientId: analysis.payload.clientId,
					clientOrigin: analysis.payload.clientOrigin,
					isValid: analysis.isValid
				});
			}
		}

		// Summary
		const accessAnalysis = tokens.accessToken ? analyzeToken(tokens.accessToken) : null;
		const refreshAnalysis = tokens.refreshToken ? analyzeToken(tokens.refreshToken) : null;

		testLogger.info('\n📊 Analysis Summary:');
		testLogger.info(`   Access Token Valid: ${accessAnalysis?.isValid ? '✅' : '❌'}`);
		testLogger.info(`   Refresh Token Valid: ${refreshAnalysis?.isValid ? '✅' : '❌'}`);
		if (accessAnalysis?.payload) {
			testLogger.info(`   User ID: ${accessAnalysis.payload.userId}`);
			testLogger.info(`   Email: ${accessAnalysis.payload.email}`);
			testLogger.info(`   Client ID: ${accessAnalysis.payload.clientId}`);
		}
		testLogger.log(`=== END ${context.toUpperCase()} ===`);
	}
    
	/** Log registration response data in a structured way */
	static logRegistrationResponse(responseData: any, context: string = 'Registration'): void {
		testLogger.info(`📊 ${context} Response Data:`, {
			hasToken: !!responseData.token,
			hasRefreshToken: !!responseData.refreshToken,
			hasUser: !!responseData.user,
			tokenLength: responseData.token?.length || 0,
			refreshTokenLength: responseData.refreshToken?.length || 0
		});
	}

	/** Format time remaining for token expiration */
	private static formatTime(exp?: number): string {
		if (!exp) return 'unknown';

		const now = Math.floor(Date.now() / 1000);
		const remaining = exp - now;

		if (remaining <= 0) return 'expired';

		const minutes = Math.floor(remaining / 60);
		return `${minutes}m`;
	}

	/** Get token lifetime in a readable format */
	private static getTokenLifetime(exp?: number): string {
		if (!exp) return 'unknown';

		const now = Math.floor(Date.now() / 1000);
		const remaining = exp - now;

		if (remaining <= 0) return 'expired';

		const minutes = Math.floor(remaining / 60);
		return `${minutes} minutes`;
	}

	/** Log token verification response */
	static logTokenVerification(response: any, context: string = 'Token Verification'): void {
		testLogger.log(`\n🔍 === ${context.toUpperCase()} ===`);
		testLogger.info('📥 Verification Response:');
		testLogger.info(`   Status: ${response.status}`);
		testLogger.info(`   Body:`, JSON.stringify(response.body, null, 2));
	}
    
	/** Log authenticated request response */
	static logAuthenticatedRequest(response: any, context: string = 'Authenticated Request'): void {
		testLogger.log(`\n🔐 === ${context.toUpperCase()} ===`);
		testLogger.info('📥 Authenticated Request Response:');
		testLogger.info(`   Status: ${response.status}`);
		testLogger.info(`   Body:`, JSON.stringify(response.body, null, 2));
	}

	/** Log user data access response */
	static logUserDataAccess(response: any, context: string = 'User Data Access'): void {
		testLogger.log(`\n👤 ${context} Result:`);
		testLogger.info(`   Status: ${response.status}`);
		testLogger.info(`   Success: ${response.body?.success}`);
		testLogger.info(`   Message: ${response.body?.message}`);
		if (response.body?.error) {
			testLogger.info(`   Error Details:`, {
				code: response.body.error.code,
				message: response.body.error.message,
				type: response.body.error.type
			});
		}
	}

	/** Log health check response */
	static logHealthCheck(response: any, endpoint: string): void {
		testLogger.log(`🔍 Testing Health Check (${endpoint})...`);
		testLogger.info(`   Status: ${response.status}`);
		testLogger.info(`   Headers: X-Request-ID = ${response.headers['x-request-id']}`);
		testLogger.info(`   Success: ${response.body?.success}`);
		testLogger.info(`   Message: ${response.body?.message}`);
	}
}