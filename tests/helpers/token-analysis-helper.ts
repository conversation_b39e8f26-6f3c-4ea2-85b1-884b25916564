import { logTokenAnalysis, analyzeToken } from '@/utils/token-analysis.utils';
import type { AuthTokens } from '../constants/test.types';
import { testLogger } from './test-logger';

/** Helper class for comprehensive token analysis and logging in tests */
export class TokenAnalysisHelper {
	/** Demonstrate Express Request.user functionality */
	static demonstrateRequestUserFunctionality(
		token: string,
		context: string = 'Express Integration'
	): void {
		testLogger.log(`\n📋 Token Analysis for ${context}:`);

		// Analyze the token
		logTokenAnalysis(token, 'Test Token');

		// Simulate what would be available in req.user
		const analysis = analyzeToken(token);
		if (analysis.isValid && analysis.payload) {
			testLogger.info('\n👤 Token would populate req.user with:');
			testLogger.info(`   userId: ${analysis.payload.userId}`);
			testLogger.info(`   email: ${analysis.payload.email}`);
			testLogger.info(`   clientId: ${analysis.payload.clientId}`);
			testLogger.info(`   clientOrigin: ${analysis.payload.clientOrigin}`);
			testLogger.info(`   tokenType: ${analysis.payload.tokenType}`);
			testLogger.info('=== END REQUEST.USER ANALYSIS ===');

			testLogger.info('\n💡 In your Express controllers, you can now use:');
			testLogger.info('   const userId = getUserId(req);');
			testLogger.info('   const email = getUserEmail(req);');
			testLogger.info('   const clientId = getClientId(req);');
			testLogger.info('   const hasAdmin = userHasPermission(req, "admin");');
		}
	}

	/** Comprehensive token demonstration showing all analysis features */
	static comprehensiveTokenDemo(
		tokens: AuthTokens,
		context: string = 'Comprehensive Demo'
	): void {
		testLogger.log(`\n🎯 === ${context.toUpperCase()} ===`);

		// Individual token analysis
		if (tokens.accessToken) {
			logTokenAnalysis(tokens.accessToken, 'Access Token');
		}
		if (tokens.refreshToken) {
			logTokenAnalysis(tokens.refreshToken, 'Refresh Token');
		}

		// Token comparison removed - method was unused

		// Extract user info
		if (tokens.accessToken) {
			const analysis = analyzeToken(tokens.accessToken);
			if (analysis.isValid && analysis.payload) {
				testLogger.info('\n👤 Extracted User Info:', {
					userId: analysis.payload.userId,
					email: analysis.payload.email,
					clientId: analysis.payload.clientId,
					clientOrigin: analysis.payload.clientOrigin,
					isValid: analysis.isValid
				});
			}
		}

		// Summary
		const accessAnalysis = tokens.accessToken ? analyzeToken(tokens.accessToken) : null;
		const refreshAnalysis = tokens.refreshToken ? analyzeToken(tokens.refreshToken) : null;

		testLogger.info('\n📊 Analysis Summary:');
		testLogger.info(`   Access Token Valid: ${accessAnalysis?.isValid ? '✅' : '❌'}`);
		testLogger.info(`   Refresh Token Valid: ${refreshAnalysis?.isValid ? '✅' : '❌'}`);
		if (accessAnalysis?.payload) {
			testLogger.info(`   User ID: ${accessAnalysis.payload.userId}`);
			testLogger.info(`   Email: ${accessAnalysis.payload.email}`);
			testLogger.info(`   Client ID: ${accessAnalysis.payload.clientId}`);
		}
		testLogger.log(`=== END ${context.toUpperCase()} ===`);
	}

	/** Log token verification response */
	static logTokenVerification(response: any, context: string = 'Token Verification'): void {
		testLogger.log(`\n🔍 === ${context.toUpperCase()} ===`);
		testLogger.info('📥 Verification Response:');
		testLogger.info(`   Status: ${response.status}`);
		testLogger.info(`   Body:`, JSON.stringify(response.body, null, 2));
	}

	/** Log authenticated request response */
	static logAuthenticatedRequest(response: any, context: string = 'Authenticated Request'): void {
		testLogger.log(`\n🔐 === ${context.toUpperCase()} ===`);
		testLogger.info('📥 Authenticated Request Response:');
		testLogger.info(`   Status: ${response.status}`);
		testLogger.info(`   Body:`, JSON.stringify(response.body, null, 2));
	}

	/** Log user data access response */
	static logUserDataAccess(response: any, context: string = 'User Data Access'): void {
		testLogger.log(`\n👤 ${context} Result:`);
		testLogger.info(`   Status: ${response.status}`);
		testLogger.info(`   Success: ${response.body?.success}`);
		testLogger.info(`   Message: ${response.body?.message}`);
		if (response.body?.error) {
			testLogger.info(`   Error Details:`, {
				code: response.body.error.code,
				message: response.body.error.message,
				type: response.body.error.type
			});
		}
	}

	/** Log health check response */
	static logHealthCheck(response: any, endpoint: string): void {
		testLogger.log(`🔍 Testing Health Check (${endpoint})...`);
		testLogger.info(`   Status: ${response.status}`);
		testLogger.info(`   Headers: X-Request-ID = ${response.headers['x-request-id']}`);
		testLogger.info(`   Success: ${response.body?.success}`);
		testLogger.info(`   Message: ${response.body?.message}`);
	}
}