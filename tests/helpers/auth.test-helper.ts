import type { TestUserCreateD<PERSON>, AuthTokens, TestUser } from '../constants/test.types';
import { EnumClientOrigin, HttpStatus, UserRoles } from 'excelytics.shared-internals';
import { TEST_VALUES } from '@/constants/test-config.constants';
import { logTokenAnalysis } from '@/utils/token-analysis.utils';
import IdentityModel from '@/models/identity.model';
import { testLogger } from './test-logger';
import supertest from 'supertest';
import mongoose from 'mongoose';
import app from '@/server';

const request = supertest(app);

export class AuthTestHelper {
	// Track created users for cleanup
	private static createdUsers: string[] = [];

	// Admin user for cleanup operations
	private static adminUser: {
		email: string;
		password: string;
		clientId: string;
		clientOrigin: number;
		accessToken?: string;
	} | null = null;

	/**
	 * Generate a unique test email to avoid conflicts
	 * @param prefix - Prefix for the email
	 * @returns Unique test email with body length between 8-13
	 */
	static generateTestEmail(prefix: string = 'test'): string {
		const timestamp = Date.now();
		const random = Math.random().toString(36).substring(7);
		return `${prefix}-${timestamp}-${random}@example.com`;
	}

	/**
	 * Create a test user object
	 * @param emailPrefix - Prefix for the email
	 * @param isAdmin - Whether the user should be an admin (logging)
	 * @returns Test user object
	 */
	static createTestUser(emailPrefix: string = 'test', isAdmin: boolean = false): TestUser {
		const newUserObj: TestUser = {
			clientId: new mongoose.Types.ObjectId(),
			clientOrigin: EnumClientOrigin.Excelytics,
			email: this.generateTestEmail(emailPrefix),
			password: 'TestPassword@123',
			isActive: true
		};

		console.log(`🔐 Creating '${isAdmin ? 'admin' : emailPrefix}' test-user:`, {
			email: newUserObj.email,
			clientId: newUserObj.clientId.toString(),
			clientOrigin: newUserObj.clientOrigin
		});

		return newUserObj;
	}

	/**
	 * Create test user data compatible with Identity model
	 * @param emailPrefix - Prefix for the email
	 * @returns Test user data for Identity model creation
	 */
	static createTestUserData(emailPrefix: string = 'test'): TestUserCreateData {
		return {
			clientId: new mongoose.Types.ObjectId().toString(),
			clientOrigin: EnumClientOrigin.Excelytics,
			email: this.generateTestEmail(emailPrefix),
			password: 'TestPassword@123',
			isActive: true,
			roles: [UserRoles.USER]
		};
	}

	/**
	 * Get admin access token
	 * @returns Admin access token if available, null otherwise
	 */
	static getAdminToken(): string | null {
		return this.adminUser?.accessToken || null;
	}

	/**
	 * Manually saves the user to the database and execute the login endpoint
	 * This allows us to acquire a valid admin token from the authentic IdP login endpoint
	 *
	 * Essentially, creates and sets up the admin user for cleanup operations
	 * @returns True if admin user is already set, false otherwise
	 */
	static async setupAdminUser(): Promise<boolean> {
		if (this.adminUser?.accessToken) {
			testLogger.log('👑 Admin user already setup');
			return true;
		}

		try {
			console.log('👑 Setting up admin user for cleanup operations...');

			const bcrypt = await import('bcryptjs');

			// Create admin user data using helper and set admin & user roles
			const adminUserData = this.createTestUserData('cleanup-admin');
			adminUserData.roles = [UserRoles.USER, UserRoles.ADMIN];

			// Hash the password and create the admin user Identity object
			const hashedPassword = await bcrypt.hash(adminUserData.password, 10);
			const newAdmin = new IdentityModel({
				...adminUserData,
				password: hashedPassword
			});

			// Manually save record to db, instead of endpoint testing:
			// This ensures endpoint testing will use a valid user from the db
			await newAdmin.save();
			testLogger.log('👑 Admin user created in database:', adminUserData.email);

			// Now, Login to acquire admin token
			await this.wait(500);
			const loginResponse = await request.post('/api/v1/auth/login').send({
				email: adminUserData.email,
				password: adminUserData.password,
				clientId: adminUserData.clientId,
				clientOrigin: adminUserData.clientOrigin
			});

			// Ensure successful response with generated admin token
			// If successful, set data to static admin user object
			if (loginResponse.status === HttpStatus.OK && loginResponse.body.data?.token) {
				this.adminUser = {
					email: adminUserData.email,
					password: adminUserData.password,
					clientId: adminUserData.clientId,
					clientOrigin: adminUserData.clientOrigin,
					accessToken: loginResponse.body.data.token
				};

				testLogger.log('👑 Admin user logged in successfully');

				// Analyze admin token
				logTokenAnalysis(this.adminUser.accessToken as string, 'Admin Token');

				return true;
			} else {
				testLogger.error(
					'❌ Failed to login admin user:',
					loginResponse.status,
					loginResponse.body
				);
				return false;
			}
		} catch (error) {
			testLogger.error('❌ Error setting up admin user:', error);
			return false;
		}
	}

	/** Adds a user email to the cleanup list */
	static trackUserForCleanup(email: string): void {
		if (!this.createdUsers.includes(email)) {
			this.createdUsers.push(email);
		}
	}

	/** Clean up all tracked users using admin privileges */
	static async cleanupAllUsers(): Promise<void> {
		if (this.createdUsers.length === 0) {
			testLogger.debug('🧹 No users to cleanup');
			return;
		}

		testLogger.log(`🧹 Starting cleanup of ${this.createdUsers.length} users...`);

		// Check if admin user is already available (should be from beforeAll)
		if (!this.adminUser?.accessToken) {
			// Only setup admin user if not already available
			testLogger.log('👑 Admin user not found, setting up for cleanup...');
			const adminReady = await this.setupAdminUser();
			if (!adminReady || !this.adminUser?.accessToken) {
				testLogger.error('❌ Cannot cleanup users - admin user not available');
				return;
			}
		} else {
			testLogger.log('👑 Admin user already setup');
		}

		// Delete all tracked users
		for (const email of this.createdUsers) {
			try {
				const deleted = await this.deleteUserAsAdmin(this.adminUser.accessToken, email);
				if (deleted) {
					testLogger.info('✅ Cleaned up user:', email);
				} else {
					testLogger.debug('⚠️ Failed to cleanup user:', email);
				}

				// Small delay between deletions
				await this.wait(200);
			} catch (error: any) {
				testLogger.debug('⚠️ Error cleaning up user:', email, error.message);
			}
		}

		// Clean up admin user itself
		try {
			await this.deleteUserAsAdmin(this.adminUser.accessToken, this.adminUser.email);
			testLogger.debug('✅ Cleaned up admin user');
		} catch (error: any) {
			testLogger.debug(
				'⚠️ Failed to cleanup admin user',
				this.adminUser.email,
				error.message
			);
		}

		// Reset tracking
		this.createdUsers = [];
		this.adminUser = null;

		testLogger.log('🧹 Cleanup completed');
	}

	/**
	 * Delete a user using admin privileges
	 * @param adminToken - Admin access token
	 * @param email - Email of user to delete
	 */
	static async deleteUserAsAdmin(adminToken: string, email: string): Promise<boolean> {
		try {
			const response = await request
				.delete(`/api/v1/identity/email/${email}`)
				.set('Authorization', `Bearer ${adminToken}`);

			testLogger.info(
				`🗑️ Delete attempt for ${email}:`,
				response.status,
				response.body?.message || 'No message'
			);

			return response.status === 200;
		} catch (error: any) {
			testLogger.error(`❌ Error deleting user ${email}:`, error.message);
			return false;
		}
	}

	/** Register a new test user and return authentication tokens */
	static async registerAndLogin(user: TestUser): Promise<AuthTokens | null> {
		try {
			// Wait to avoid rate limiting
			await this.wait(1000);

			// Register the user
			const registerResponse = await request.post('/api/v1/auth/register').send(user);

			if (
				![200, 201].includes(registerResponse.status) ||
				!registerResponse.body.data?.token
			) {
				testLogger.error(
					'❌ Registration failed:',
					registerResponse.status,
					registerResponse.body
				);
				return null;
			}

			testLogger.log('✅ Registration successful for:', user.email);

			// Track user for cleanup
			this.trackUserForCleanup(user.email);
			testLogger.info('📊 Registration Response Data:', {
				hasToken: !!registerResponse.body.data.token,
				hasRefreshToken: !!registerResponse.body.data.refreshToken,
				hasUser: !!registerResponse.body.data.user,
				tokenLength: registerResponse.body.data.token?.length || 0,
				refreshTokenLength: registerResponse.body.data.refreshToken?.length || 0
			});

			// Analyze the tokens using our utilities
			if (registerResponse.body.data.token) {
				logTokenAnalysis(
					registerResponse.body.data.token,
					'Access Token from Registration'
				);
			}
			if (registerResponse.body.data.refreshToken) {
				logTokenAnalysis(
					registerResponse.body.data.refreshToken,
					'Refresh Token from Registration'
				);
			}

			if (registerResponse.body.data.user) {
				testLogger.info('👤 User object from registration:', {
					id: registerResponse.body.data.user._id || registerResponse.body.data.user.id,
					email: registerResponse.body.data.user.email,
					clientId: registerResponse.body.data.user.clientId,
					clientOrigin: registerResponse.body.data.user.clientOrigin,
					isActive: registerResponse.body.data.user.isActive,
					hasPassword: !!registerResponse.body.data.user.password,
					createdAt: registerResponse.body.data.user.createdAt,
					updatedAt: registerResponse.body.data.user.updatedAt
				});
			}

			return {
				accessToken: registerResponse.body.data.token,
				refreshToken: registerResponse.body.data.refreshToken || '',
				user: registerResponse.body.data.user
			} as AuthTokens;
		} catch (error) {
			testLogger.error('Error in registerAndLogin:', error);
			return null;
		}
	}

	/** Wait for specified milliseconds */
	static async wait(ms: number): Promise<void> {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}

	/** Generate an expired token for testing */
	static async generateExpiredToken(): Promise<string> {
		// This would need to be implemented based on your JWT service
		// For now, return a malformed token that will be rejected
		return TEST_VALUES.EXPIRED_TOKEN;
	}

	/** Generate a malformed token for testing */
	static generateMalformedToken(): string {
		return TEST_VALUES.MALFORMED_TOKEN;
	}

	/** Test rate limiting by making multiple requests */
	static async testRateLimit(
		endpoint: string,
		payload: any,
		maxAttempts: number = 15
	): Promise<number[]> {
		const promises = [];
		for (let i = 0; i < maxAttempts; i++) {
			promises.push(request.post(endpoint).send(payload));
		}

		const responses = await Promise.all(promises);
		return responses.map((res) => res.status);
	}
}

export default AuthTestHelper;
