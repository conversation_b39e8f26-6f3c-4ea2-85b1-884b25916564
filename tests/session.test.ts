import { beforeAll, describe, afterAll, expect, it } from 'bun:test';
import { EnumClientOrigin } from 'excelytics.shared-internals';
import { AuthTestHelper } from './helpers/auth.test-helper';
import supertest from 'supertest';
import mongoose from 'mongoose';
import app from '@/server';

const request = supertest(app);

// Helper function to safely get cookies array
const getCookiesArray = (setCookieHeader: string | string[] | undefined): string[] => {
	if (!setCookieHeader) return [];
	return Array.isArray(setCookieHeader) ? setCookieHeader : [setCookieHeader];
};

// Test user for session tests
const sessionTestUser = {
	clientId: new mongoose.Types.ObjectId(),
	clientOrigin: EnumClientOrigin.Excelytics,
	email: '<EMAIL>',
	password: 'SessionTest@123',
	isActive: true
};

describe('Session Management Tests', () => {
	beforeAll(async () => {
		// Create test user (if it already exists, registration will fail gracefully)
		try {
			await request.post('/api/v1/auth/register').send(sessionTestUser);
		} catch (error) {
			// User might already exist, that's okay
			console.log('Session test user might already exist, continuing...');
		}
	});

	afterAll(async () => {
		// Track the session test user for cleanup
		AuthTestHelper.trackUserForCleanup(sessionTestUser.email);

		// Clean up all users (this will be handled by the global cleanup)
		console.log('Session tests completed - cleanup will be handled globally');
	});

	describe('Session Cookie Tests', () => {
		it('Should set session cookie on session endpoint', async () => {
			const response = await request.get('/api/v1/auth/session');

			expect(response.status).toBe(200);
			expect(response.header['set-cookie']).toBeDefined();
			
			const cookies = Array.isArray(response.header['set-cookie'])
				? response.header['set-cookie']
				: [response.header['set-cookie']];
			const sessionCookie = cookies.find((cookie: string) =>
				cookie.includes('excelytics.idp.sid')
			);
			expect(sessionCookie).toBeDefined();
		});

		it('Should include proper cookie security attributes', async () => {
			const response = await request.get('/api/v1/auth/session');

			expect(response.status).toBe(200);
			const cookies = Array.isArray(response.header['set-cookie'])
				? response.header['set-cookie']
				: response.header['set-cookie'] ? [response.header['set-cookie']] : [];
			const sessionCookie = cookies.find((cookie: string) =>
				cookie.includes('excelytics.idp.sid')
			);

			if (sessionCookie) {
				expect(sessionCookie).toContain('HttpOnly');
				expect(sessionCookie).toContain('Path=/');
				// In development, secure might not be set
				// expect(sessionCookie).toContain('Secure');
			}
		});

		it('Should set appropriate SameSite attribute', async () => {
			const response = await request.get('/api/v1/auth/session');

			expect(response.status).toBe(200);
			const cookies = getCookiesArray(response.header['set-cookie']);
			const sessionCookie = cookies.find((cookie: string) =>
				cookie.includes('excelytics.idp.sid')
			);

			if (sessionCookie) {
				// Should have SameSite attribute (lax for dev, strict for prod)
				expect(sessionCookie).toMatch(/SameSite=(lax|strict)/i);
			}
		});

		it('Should maintain session across requests', async () => {
			// First request to establish session
			const firstResponse = await request.get('/api/v1/auth/session');
			expect(firstResponse.status).toBe(200);
			
			const cookies = getCookiesArray(firstResponse.header['set-cookie']);
			const sessionCookie = cookies.find((cookie: string) =>
				cookie.includes('excelytics.idp.sid')
			);

			if (sessionCookie) {
				// Extract cookie value for subsequent request
				const cookieValue = sessionCookie.split(';')[0];
				
				// Second request with session cookie
				const secondResponse = await request
					.get('/api/v1/auth/session')
					.set('Cookie', cookieValue);

				expect(secondResponse.status).toBe(200);
			}
		});
	});

	describe('Session Security Tests', () => {
		it('Should generate unique session IDs', async () => {
			const response1 = await request.get('/api/v1/auth/session');
			const response2 = await request.get('/api/v1/auth/session');

			expect(response1.status).toBe(200);
			expect(response2.status).toBe(200);

			const cookies1 = getCookiesArray(response1.header['set-cookie']);
			const cookies2 = getCookiesArray(response2.header['set-cookie']);
			const cookie1 = cookies1.find((cookie: string) =>
				cookie.includes('excelytics.idp.sid')
			);
			const cookie2 = cookies2.find((cookie: string) =>
				cookie.includes('excelytics.idp.sid')
			);

			if (cookie1 && cookie2) {
				expect(cookie1).not.toBe(cookie2);
			}
		});

		it('Should handle session regeneration properly', async () => {
			// This would test session regeneration after login
			// For now, just verify session endpoint works
			const response = await request.get('/api/v1/auth/session');
			expect(response.status).toBe(200);
		});

		it('Should not expose session data in responses', async () => {
			const response = await request.get('/api/v1/auth/session');

			expect(response.status).toBe(200);
			expect(response.body).not.toHaveProperty('sessionData');
			expect(response.body).not.toHaveProperty('sessionSecret');
		});
	});

	describe('Session Persistence Tests', () => {
		it('Should persist session data in MongoDB store', async () => {
			// This test verifies that sessions are stored in MongoDB
			// We can't directly check the database, but we can verify behavior
			const response = await request.get('/api/v1/auth/session');
			
			expect(response.status).toBe(200);
			expect(response.header['set-cookie']).toBeDefined();
		});

		it('Should handle session expiration', async () => {
			// Create a session
			const response = await request.get('/api/v1/auth/session');
			expect(response.status).toBe(200);
			
			const cookies = getCookiesArray(response.header['set-cookie']);
			const sessionCookie = cookies.find((cookie: string) =>
				cookie.includes('excelytics.idp.sid')
			);

			if (sessionCookie) {
				// Check that cookie has Max-Age or Expires
				expect(sessionCookie).toMatch(/(Max-Age|Expires)/i);
			}
		});

		it('Should clean up expired sessions', async () => {
			// This test would verify that expired sessions are cleaned up
			// For now, just verify the session endpoint works
			const response = await request.get('/api/v1/auth/session');
			expect(response.status).toBe(200);
		});
	});

	describe('Session Configuration Tests', () => {
		it('Should use correct session name', async () => {
			const response = await request.get('/api/v1/auth/session');

			expect(response.status).toBe(200);
			const cookies = getCookiesArray(response.header['set-cookie']);
			const sessionCookie = cookies.find((cookie: string) =>
				cookie.includes('excelytics.idp.sid')
			);

			expect(sessionCookie).toBeDefined();
			expect(sessionCookie).toContain('excelytics.idp.sid');
		});

		it('Should handle session initialization correctly', async () => {
			// Test that sessions are properly initialized when saveUninitialized is true
			const response = await request.get('/api/v1/health');

			expect(response.status).toBe(200);
			// With saveUninitialized: true, session cookies may be set
			const cookies = getCookiesArray(response.header['set-cookie']);
			const sessionCookie = cookies.find((cookie: string) =>
				cookie.includes('excelytics.idp.sid')
			);

			// Session cookie may or may not be set depending on middleware configuration
			// This is acceptable behavior for testing environment
			if (sessionCookie) {
				expect(sessionCookie).toContain('excelytics.idp.sid');
				expect(sessionCookie).toContain('HttpOnly');
			}
		});

		it('Should handle concurrent session requests', async () => {
			// Test multiple simultaneous session requests
			const promises = Array(5).fill(null).map(() => 
				request.get('/api/v1/auth/session')
			);

			const responses = await Promise.all(promises);
			
			responses.forEach(response => {
				expect(response.status).toBe(200);
				expect(response.header['set-cookie']).toBeDefined();
			});
		});
	});

	describe('Session Error Handling', () => {
		it('Should handle session store connection errors gracefully', async () => {
			// This would test behavior when MongoDB session store is unavailable
			// For now, just verify normal operation
			const response = await request.get('/api/v1/auth/session');
			expect(response.status).toBe(200);
		});

		it('Should handle malformed session cookies', async () => {
			const response = await request
				.get('/api/v1/auth/session')
				.set('Cookie', 'excelytics.idp.sid=malformed-session-id');

			// Should still work, might create new session
			expect(response.status).toBe(200);
		});

		it('Should handle session store write failures', async () => {
			// This would test behavior when session cannot be saved
			// For now, just verify the endpoint responds
			const response = await request.get('/api/v1/auth/session');
			expect(response.status).toBe(200);
		});
	});

	describe('Session Integration with Authentication', () => {
		it('Should work alongside JWT authentication', async () => {
			// Login to get JWT token
			const loginResponse = await request
				.post('/api/v1/auth/login')
				.send({
					email: sessionTestUser.email,
					password: sessionTestUser.password
				});

			expect(loginResponse.status).toBe(200);
			
			// Get session while authenticated
			const sessionResponse = await request
				.get('/api/v1/auth/session')
				.set('Authorization', `Bearer ${loginResponse.body.data.token}`);

			expect(sessionResponse.status).toBe(200);
		});

		it('Should not interfere with token-based authentication', async () => {
			// Verify that session middleware doesn't break JWT auth
			const loginResponse = await request
				.post('/api/v1/auth/login')
				.send({
					email: sessionTestUser.email,
					password: sessionTestUser.password
				});

			expect(loginResponse.status).toBe(200);
			expect(loginResponse.body.data).toHaveProperty('token');
		});
	});
});