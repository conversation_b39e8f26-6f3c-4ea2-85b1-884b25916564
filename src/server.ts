// Imported first to patch console
import { initializeConsoleLogger } from '@/config/init-console.logger';

initializeConsoleLogger();

// Import shared types (including express augmentation)
import 'excelytics.shared-internals/types';
import {
	InitializeGracefulShutdown,
	GlobalErrorHandler,
	NodeServerErrors,
	ServerConstants,
	NotFoundError
} from 'excelytics.shared-internals';
import EarlyCustomHeaders from '@/middleware/custom.middleware';
import type { NextFunction, Response, Request } from 'express';
import { setLogLevel } from '@typegoose/typegoose';
import SetupGlobalMiddleware from '@/middleware';
import { env_idp } from '@app-types/env_';
import SetupRoutes from '@/routes/index';
import { connectDB } from '@/config/db';
import mongoose from 'mongoose';
import express from 'express';

// Set Typegoose log level if used
if (env_idp.LOG_LEVEL && typeof setLogLevel === 'function') {
	setLogLevel(env_idp.LOG_LEVEL as any);
}

const SERVICE_NAME = env_idp.SERVICE_NAME;
const API_VERSION = env_idp.API_VERSION;
const PORT = env_idp.PORT;
const app = express();

// Global middleware setup
EarlyCustomHeaders(app);
SetupGlobalMiddleware(app);

// API Routes (e.g. http://localhost:6060/api/v1/...)
SetupRoutes(app);

// Catch-all for 404 Not Found
app.use((req: Request, res: Response, next: NextFunction) => {
	// This is of type BaseError (correct for next() to log better)
	const err = new NotFoundError(
		`The requested resource ${req.method} ${req.originalUrl} was not found on this server`
	);

	// Pass to the global error handler
	next(err);
});

// Global Error Handler - Use the shared GlobalErrorHandler for all environments
app.use(GlobalErrorHandler);

const StartServer = async () => {
	let server: import('http').Server | null = null;

	try {
		await connectDB();

		server = app.listen(PORT, () => {
			console.log(`🚀 Server: '${SERVICE_NAME}' is running on port ${PORT}`);
			console.log(`📊 Environment: ${env_idp.ENV}`);
			console.log(`🔗 API Base: /api/${API_VERSION}`);
			console.log(`💾 Log Level: ${env_idp.LOG_LEVEL}`);
			console.log(`🏥 Health Check: http://localhost:${PORT}/api/${API_VERSION}/health`);
			console.log(
				`🏥 Deeper Health Check: http://localhost:${PORT}/api/${API_VERSION}/health/all`
			);
			console.log(
				'------------------------------------------------------------------------------\n'
			);
		});

		// Initialize graceful shutdown mechanisms
		InitializeGracefulShutdown(server, mongoose, undefined, SERVICE_NAME);

		// Handle server errors
		server.on(ServerConstants.ERROR, (error: any) => {
			if (error.syscall !== NodeServerErrors.LISTEN_SYSCALL) {
				throw error;
			}

			switch (error.code) {
				case NodeServerErrors.ACCESS_DENIED:
					console.error(`${SERVICE_NAME}: Port ${PORT} requires elevated privileges`);
					process.exit(1);
					break;
				case NodeServerErrors.ADDRESS_IN_USE:
					console.error(`${SERVICE_NAME}: Port ${PORT} is already in use`);
					process.exit(1);
					break;
				default:
					console.error(
						`${SERVICE_NAME}: Unhandled listen error on port ${PORT}:`,
						error
					);
					throw error;
			}
		});
	} catch (error) {
		console.error(`❌ Failed to start ${SERVICE_NAME} server:`, error);
		if (server) {
			server.close(() => {
				mongoose.disconnect().finally(() => process.exit(1));
			});
		} else {
			mongoose.disconnect().finally(() => process.exit(1));
		}
	}
};

await StartServer();

// Used in integration tests
export default app;
