import { HelmetConfiguration, CorsConfiguration } from '@/middleware/security/security-options.middleware';
import { ServerConstants, accessLogStream, HttpStatus, EnumEnv } from 'excelytics.shared-internals';
import { SessionMiddleware } from '@/middleware/security/cookie-security.middleware';
import type { NextFunction, Response, Request } from 'excelytics.shared-internals';
import { RateLimiters } from '@/middleware/rate-limit.middleware';
import { env_idp } from '@app-types/env_';
import cookieParser from 'cookie-parser';
import express from 'express';
import morgan from 'morgan';
import cors from 'cors';

const API_BASE_PATH = `/api/${env_idp.API_VERSION}`;

const SetupGlobalMiddleware = (app: express.Application) => {
	// 0. Trust Proxy: Essential for accurate IP determination if behind a proxy
	// Must be set before any middleware that relies on `req.ip`
	if (env_idp.ENV !== EnumEnv.Development) {
		// Adjust '1' if you have multiple proxies
		app.set('trust proxy', 1);
	}

	// 1. General Rate Limiting: Applied early to protect against basic abuse
	app.use(API_BASE_PATH, RateLimiters.GeneralLimiter);

	// 2. Security Middleware / Headers
	app.use(HelmetConfiguration);
	app.use(cors(CorsConfiguration));

	// 3. Body Parsers: Parse incoming request bodies (Limit for IdP, adjust if necessary)
	// Applied after general rate limiting to avoid parsing bodies of already rejected requests
	app.use(
		express.json({
			limit: '10mb',
			verify: (req: Request, res, buf, _) => {
				// Optional: Log large raw request buffers for monitoring
				if (buf.length > 1024 * 1024) {
					// 1MB
					console.warn(
						`Large JSON request buffer: ${buf.length} bytes from ${req.ip || 'unknown IP'} for ${req.originalUrl}`
					);
				}
			},
		})
	);

	// Default is 1000, good to be explicit
	app.use(express.urlencoded({ extended: true, limit: '10mb', parameterLimit: 1000 }));

	// 4. Cookie Parser: Parse cookies before session middleware
	app.use(cookieParser(env_idp.COOKIE_SECRET || 'default-cookie-secret'));

	// 5. Session Middleware: Manage user sessions.
	app.use(SessionMiddleware.SessionOptions);

	// 6. HTTP Request Logging (Log to file in production)
	if (env_idp.ENV === EnumEnv.Development) {
		app.use(morgan('dev'));
	} else {
		app.use(
			morgan('combined', {
				stream: accessLogStream,
				// Only log errors in production
				skip: (req, res) => res.statusCode < 400 && env_idp.ENV === EnumEnv.Production,
			})
		);
	}

	// 7. Specific Rate Limiters for Sensitive Routes
	// These stack with the generalLimiter for these specific paths.
	app.use(`${API_BASE_PATH}/auth/login`, RateLimiters.AuthenticationLimiter);
	app.use(`${API_BASE_PATH}/auth/register`, RateLimiters.AuthenticationLimiter);
	app.use(`${API_BASE_PATH}/auth/reset-password`, RateLimiters.PasswordResetLimiter);

	// 8. Request Processing Time Monitoring
	// Useful for identifying slow endpoints
	app.use((req: Request, res: Response, next: NextFunction) => {
		const startTime = process.hrtime();

		res.on(ServerConstants.FINISH, () => {
			const diff = process.hrtime(startTime);
			const durationMs = diff[0] * 1000 + diff[1] / 1000000; // More precise duration
			const thresholdMs = (env_idp.SLOW_REQUEST_THRESHOLD_MS as number) || 5000;
			if (durationMs > thresholdMs) {
				console.warn(
					`Slow request: ${req.method} ${req.originalUrl} took ${durationMs.toFixed(3)}ms from IP ${req.ip}`
				);
			}
		});
		next();
	});

	// --- Add this block to handle favicon requests gracefully ---
	// This creates a specific route for /favicon.ico
	// Any request matching it will be handled here and will NOT fall through to the 404 handler
	app.get('/favicon.ico', (req: Request, res: Response) => {
		res.status(HttpStatus.NO_CONTENT).send();
	});
};

export default SetupGlobalMiddleware;
