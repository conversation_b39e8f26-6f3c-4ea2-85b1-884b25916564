import { ErrorCodes, EnumEnv } from 'excelytics.shared-internals';
import rateLimit from 'express-rate-limit';
import { env_idp } from '@app-types/env_';

/**
 * Get environment-specific rate limits for general API usage
 * Balances security with usability for different environments
 */
function getGeneralRateLimit(): number {
	switch (env_idp.ENV) {
		case EnumEnv.Development:
			return 1000; // High limit for development testing
		case EnumEnv.Test:
			return 1000; // High limit for automated testing
		case EnumEnv.UAT:
			return 500; // Moderate limit for user acceptance testing
		case EnumEnv.Staging:
			return 300; // Production-like but slightly higher for testing
		case EnumEnv.Production:
			return 200; // Conservative limit for production
		default:
			return 200; // Default to production settings
	}
}

/**
 * Get environment-specific rate limits for authentication endpoints
 * Stricter limits to prevent brute force attacks
 */
function getAuthRateLimit(): number {
	switch (env_idp.ENV) {
		case EnumEnv.Development:
			return 50; // Higher for development testing
		case EnumEnv.Test:
			return 100; // Very high for automated testing
		case EnumEnv.UAT:
			return 25; // Moderate for user testing
		case EnumEnv.Staging:
			return 15; // Close to production
		case EnumEnv.Production:
			return 10; // Strict for production security
		default:
			return 10; // Default to production settings
	}
}

/**
 * Get environment-specific rate limits for password reset
 * Very strict to prevent abuse of password reset functionality
 */
function getPasswordResetRateLimit(): number {
	switch (env_idp.ENV) {
		case EnumEnv.Development:
			return 20; // Higher for development testing
		case EnumEnv.Test:
			return 50; // High for automated testing
		case EnumEnv.UAT:
			return 10; // Moderate for user testing
		case EnumEnv.Staging:
			return 7; // Close to production
		case EnumEnv.Production:
			return 5; // Very strict for production
		default:
			return 5; // Default to production settings
	}
}

// General API rate limiting
// Counts all requests to provide a baseline protection against general abuse
const GeneralLimiter = rateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	limit: getGeneralRateLimit(), // Environment-specific limits
	legacyHeaders: false,
	standardHeaders: 'draft-7', // Recommended standard for future compatibility
	skipSuccessfulRequests: false, // Default is false, counts all requests.

	// For a general limiter, counting all requests is usually preferred.
	// If you specifically want to only count failing requests for the general IdP limiter,
	// you can set skipSuccessfulRequests: true, but this is less common for a general limiter.
	message: {
		success: false,
		message: 'Too many failed attempts, please try again later.',
		error: {
			code: ErrorCodes.RATE_LIMIT_EXCEEDED,
			message: 'Rate limit exceeded'
		}
	}
});

// Authentication endpoints (stricter, skips successful attempts)
const AuthenticationLimiter = rateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	limit: getAuthRateLimit(), // Environment-specific limits
	legacyHeaders: false,
	standardHeaders: 'draft-7',
	skipSuccessfulRequests: true, // Only count failed login/registration attempts
	message: {
		success: false,
		message: 'Too many authentication attempts, please try again later.',
		error: {
			code: ErrorCodes.AUTH_RATE_LIMIT_EXCEEDED,
			message: 'Authentication rate limit exceeded'
		}
	}
});

// Password reset (very strict, counts all attempts)
const PasswordResetLimiter = rateLimit({
	windowMs: 60 * 60 * 1000, // 1 hour
	limit: getPasswordResetRateLimit(), // Environment-specific limits
	legacyHeaders: false,
	standardHeaders: 'draft-7',
	skipSuccessfulRequests: false, // Default is false, count all password reset attempts
	message: {
		success: false,
		message: 'Too many password reset attempts, please try again later.',
		error: {
			code: ErrorCodes.PASSWORD_RESET_RATE_LIMIT_EXCEEDED,
			message: 'Password reset rate limit exceeded'
		}
	}
});

export const RateLimiters = {
	GeneralLimiter,
	PasswordResetLimiter,
	AuthenticationLimiter
};
