import type { NextFunction, Response, Request } from 'excelytics.shared-internals';
import { ServiceNames, EnumEnv } from 'excelytics.shared-internals';
import { env_idp } from '@app-types/env_';
import compression from 'compression';
import { v4 as uuidv4 } from 'uuid';
import express from 'express';

// Early security headers and request tracking
const EarlyCustomHeaders = (app: express.Application) => {
	app.use((req: Request, res: Response, next: NextFunction) => {
		// Generate unique request ID for tracing, use existing if passed by proxy/gateway
		const requestId = req.headers['x-request-id'] || uuidv4();
		req.headers['x-request-id'] = requestId;
		res.setHeader('X-Request-ID', requestId);

		// Service identification headers
		res.setHeader('X-Service', ServiceNames.IDENTITY);
		res.setHeader('X-Environment', env_idp.ENV as string);
		res.setHeader('X-API-Version', env_idp.API_VERSION as string);

		// Security headers
		res.setHeader('X-Frame-Options', 'DENY');
		res.setHeader('X-Content-Type-Options', 'nosniff');
		res.setHeader('X-XSS-Protection', '1; mode=block');

		// Remove server signature
		// Helmet's `hidePoweredBy` is preferred, but this is an explicit removal
		res.removeHeader('X-Powered-By');

		next();
	});

	// Compression for non-development environments
	if (env_idp.ENV !== EnumEnv.Development) {
		app.use(
			compression({
				filter: (req: Request, res: Response) => {
					// Don't compress responses if this request header is present
					if (req.headers['x-no-compression']) {
						return false;
					}
					// Fallback to standard filter function
					return compression.filter(req, res);
				},
				level: 6, // Good balance of compression and speed
				threshold: 1024 // Only compress if responses are larger than 1KB
			})
		);
	}
};

export default EarlyCustomHeaders;
