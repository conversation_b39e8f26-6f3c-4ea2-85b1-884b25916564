import { CustomErrorResponse, ValidationError, ErrorCodes } from 'excelytics.shared-internals';
import type { NextFunction, Response, Request } from 'excelytics.shared-internals';
import { z } from 'zod';

// An object to hold different validation schemas
export const validationSchema = {
	// Schema for routes that expect an email in the URL parameters
	emailParam: z.object({
		email: z.string().email('A valid email address must be provided in the URL'),
	}),
	// Schema for routes that expect a clientId in the URL parameters
	clientIdParam: z.object({
		// Add more specific validation if clientId has a known format (e.g., is a MongoDB ObjectId string)
		clientId: z.string().min(1, 'A client ID must be provided in the URL'),
	}),
	// Add more schemas for query params or other param shapes as needed
};

/**
 * Middleware factory to validate request parameters against a Zod schema.
 * @param schema The Zod schema to validate against req.params.
 */
export const validateRequestParams = (schema: z.ZodObject<any>) => {
	return (req: Request, res: Response, next: NextFunction) => {
		const validationResult = schema.safeParse(req.params);
		if (!validationResult.success) {
			// Use your custom error responder for consistency
			new CustomErrorResponse().SendValidationError(
				res,
				validationResult.error,
				'Invalid URL parameter(s)',
				ErrorCodes.INVALID_PARAMETER
			);
			return;
		}
		// If validation is successful, proceed to the controller
		next();
	};
};
