import {
	type TokenPayload,
	UnauthorizedError,
	Enum<PERSON>ermissions,
	ForbiddenError,
	UserRoles,
} from 'excelytics.shared-internals';
import type { AuthenticatedRequest } from '@/interfaces/auth.interface';
import type { NextFunction, Response } from 'express';
import IdentityModel from '@/models/identity.model';
import { JwtService } from '@/services/jwt.service';

const jwtService = new JwtService();

/**
 * @middleware requireAuth
 * @description Verifies a JWT access token from the Authorization header.
 * If the token is valid and the user is active, it attaches the user payload to `req.user` and calls next().
 * Otherwise, it passes the error to the global error handler.
 */
export const requireAuth = async (
	request: AuthenticatedRequest,
	response: Response,
	next: NextFunction
): Promise<void> => {
	try {
		// 1. Get the token from the 'Authorization' header
		const authHeader = request.headers.authorization;
		if (!authHeader || !authHeader.startsWith('Bearer ')) {
			return next(new UnauthorizedError('Authentication token is required'));
		}

		// Extract token from "Bearer <token>"
		const token = authHeader.split(' ')[1];

		// 2. Verify the token using your JWT utility
		const decodedPayload: TokenPayload | null = jwtService.verifyAccessTokenInternal(token);
		if (!decodedPayload) {
			// This handles invalid signature, expiration, etc.
			return next(new UnauthorizedError('Invalid or expired authentication token'));
		}

		// 3. (Optional but Recommended) Check if the user still exists and is active in the DB
		// This prevents a user with a valid token from accessing resources after their account has been disabled.
		const user = await IdentityModel.findById(decodedPayload.userId).select('isActive').lean();
		if (!user) {
			return next(new UnauthorizedError('User associated with this token no longer exists'));
		}
		if (!user.isActive) {
			return next(new ForbiddenError('User account is inactive'));
		}

		// 4. Attach the user payload to the request object for downstream use
		// The payload from the token is what we trust for this request's context.
		request.user = {
			userId: decodedPayload.userId,
			email: decodedPayload.email,
			clientId: decodedPayload.clientId,
			clientOrigin: decodedPayload.clientOrigin,
			clientPath: decodedPayload.clientPath,
			isActive: decodedPayload.isActive,
			tokenType: decodedPayload.tokenType,
			issuedAt: decodedPayload.issuedAt,

			// Using the permissions from the token, not a fresh DB query, for performance.
			// The permissions in the token represent the user's state *at the time of login*.
			roles: 'permissions' in decodedPayload ? decodedPayload.permissions : undefined,
		};

		// 5. Proceed to the next middleware or route handler
		next();
	} catch (error) {
		// Only catch unexpected errors (like database connection issues)
		// Pass any error to the global error handler
		next(error);
	}
};

// Create a type from the object's values: 'admin' | 'user' | 'guest'
type UserRole = (typeof UserRoles)[keyof typeof UserRoles];

/**
 * @middlewareFactory requireRole
 * @description Creates a middleware that checks if the authenticated user has a specific role. MUST be used after the `requireAuth` middleware.
 * @param requiredRole The role required to access the route.
 */
export const requireRole = (requiredRole: UserRole) => {
	return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
		// 1. Check if req.user exists (it should if requireAuth ran first)
		if (!req.user) {
			// This indicates a server-side logic error (middleware order is wrong)
			return next(new UnauthorizedError('No user found on request. Authentication may have failed.'));
		}

		// 2. Check if the user's roles/permissions include the required role
		if (!req.user?.roles?.includes(requiredRole)) {
			// User is authenticated, but not authorized for this action.
			return next(new ForbiddenError('You do not have sufficient permissions to access this resource.'));
		}

		// 3. User has the required role, proceed.
		next();
	};
};

/**
 * @middleware requireAdminRole
 * @description Checks if the user has admin permissions (not just admin role)
 * This checks for actual admin permissions in the token rather than role strings
 */
export const requireAdminRole = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
	// User must be authenticated first
	if (!req.user) {
		return next(new UnauthorizedError('No user found on request. Authentication may have failed.'));
	}

	// Check if user has admin permissions (any admin permission indicates admin access)
	const adminPermissions = [
		EnumPermissions.ADMIN_SYSTEM,
		EnumPermissions.MANAGE_USERS,
		EnumPermissions.IDENTITY_ADMIN,
		EnumPermissions.FINANCE_ADMIN,
		EnumPermissions.CALC_ADMIN,
		EnumPermissions.CLIENT_ADMIN,
	];

	const hasAdminPermission = req.user.roles?.some(permission => adminPermissions.includes(permission as any));

	if (!hasAdminPermission) {
		return next(new ForbiddenError('Admin permissions required to access this resource.'));
	}

	next();
};
