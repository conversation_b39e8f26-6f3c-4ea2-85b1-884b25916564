import {
	type AccessTokenPayload,
	CustomErrorResponse,
	UnauthorizedError,
	type TokenPayload,
	VerifyTokenSchema,
	type NextFunction,
	EnumTokenType,
	type Response,
	ServiceNames,
	ErrorCodes,
	HttpStatus,
	BaseError
} from 'excelytics.shared-internals';
import type { ExcelyticsServiceResultRefined } from '@app-types/service.types';
import { AuthenticationService } from '@/services/authentication.service';
import type { AuthenticatedRequest } from '@/interfaces/auth.interface';
import { JwtService } from '@/services/jwt.service';

export class UtilityMiddleware {
	private readonly authenticationService: AuthenticationService;
	private readonly errorResponder: CustomErrorResponse;
	private readonly jwtService: JwtService;

	constructor() {
		this.authenticationService = new AuthenticationService();
		this.errorResponder = new CustomErrorResponse();
		this.jwtService = new JwtService();
	}

	// Middleware for Token Introspection (OAuth2 style - RFC 7662)
	// Handles the entire request/response cycle for the token introspection endpoint
	public async introspectionEndpointHandler(
		request: AuthenticatedRequest,
		response: Response,
		next: NextFunction
	): Promise<void> {
		try {
			const validationResult = VerifyTokenSchema.safeParse(request.body);
			if (!validationResult.success) {
				this.errorResponder.SendValidationError(
					response,
					validationResult.error,
					'Invalid introspection request body'
				);
				return;
			}

			const { token } = validationResult.data;

			const serviceResult: ExcelyticsServiceResultRefined =
				await this.authenticationService.introspectToken(token);
			if (!serviceResult.success || !serviceResult.data) {
				// If the service operation itself failed (e.g., DB error during user check)
				this.errorResponder.SendError(
					response,
					serviceResult.error ||
						new BaseError(
							'Introspection process failed',
							HttpStatus.INTERNAL_SERVER_ERROR,
							ErrorCodes.INTROSPECTION_FAILED
						)
				);
				return;
			}

			// Standard response for inactive token.
			if (!serviceResult.data.active) {
				// Token is invalid, expired, or user is inactive.
				response.status(HttpStatus.OK).json({ active: false });
				return;
			}

			// Token is active. Prepare RFC 7662 compliant response.
			const responsePayload: any = { active: true };
			if (serviceResult.data.claims) {
				const claims = serviceResult.data.claims;

				responsePayload.iat = Math.floor(claims.issuedAt.getTime() / 1000);
				// This ensures our consuming services know the issuer came from the Excelytics IdP
				responsePayload.iss = ServiceNames.IDENTITY;
				responsePayload.client_id = claims.clientId;
				responsePayload.username = claims.email;
				responsePayload.sub = claims.userId; // Subject (user ID)
				if (claims.expiresAt) {
					responsePayload.exp = Math.floor(claims.expiresAt.getTime() / 1000);
				}

				// Or specific type if not Bearer (e.g., JWT)
				responsePayload.token_type =
					claims.tokenType === EnumTokenType.ACCESS ? 'Bearer' : 'N_A';

				if (claims.tokenType === EnumTokenType.ACCESS && 'permissions' in claims) {
					responsePayload.scope = (claims as AccessTokenPayload).permissions?.join(' ');
				}

				// Add other claims like 'aud' (audience) if present in your TokenPayload
				responsePayload.client_id = claims.clientOrigin;
				responsePayload.client_path = claims.clientPath;
			}

			response.status(HttpStatus.OK).json(responsePayload);
		} catch (error: any) {
			// Catch unexpected errors from this middleware / controller logic itself
			console.error('Introspection endpoint handler unexpected error:', error);
			next(
				new BaseError(
					'Token introspection process failed unexpectedly.',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.UNEXPECTED_ERROR,
					undefined,
					error
				)
			);
		}
	}

	// Standalone utility to verify an access token (used by /verify-access-token route)
	public async verifyAccessTokenUtility(
		token: string
	): Promise<{ valid: boolean; user?: TokenPayload; error?: BaseError }> {
		try {
			const decodedPayload: TokenPayload = this.jwtService.verifyAccessTokenInternal(token);

			if (!decodedPayload || decodedPayload.tokenType !== EnumTokenType.ACCESS) {
				return {
					valid: false,
					error: new UnauthorizedError('Invalid or expired access token.')
				};
			}

			// Optionally: Add a quick DB check for user status here if this utility is for sensitive operations
			// const userCheck = await IdentityModel.findById(decodedPayload.userId).select('isActive').lean();
			// if (!userCheck || !userCheck.isActive) {
			//     return { valid: false, error: new ForbiddenError('User inactive or not found')};
			// }

			return { valid: true, user: decodedPayload };
		} catch (error: any) {
			// JWT verification failed - return invalid but don't throw
			return {
				valid: false,
				error:
					error instanceof BaseError
						? error
						: new UnauthorizedError('Invalid or expired access token.')
			};
		}
	}
}