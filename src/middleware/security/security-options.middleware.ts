import { AllowedOrigins, EnumEnv } from 'excelytics.shared-internals';
import { env_idp } from '@app-types/env_';
import helmet from 'helmet';

//! Created with Claude 3.7 Sonnet (DO NOT REMOVE COMMENTS) (21.03.2025)
//! Updated with Gemini 2.5 Pro (03.06.2025)

//! CORS Options for IdP Service
//! Comprehensive CORS configuration for microservices architecture
//! Allows necessary headers for authentication, tracing, caching, and pagination
export const CorsConfiguration = {
	origin: function (origin: any, callback: any) {
		// Allow requests with no origin (e.g. Mobile apps, curl requests, same-origin)
		if (!origin) {
			return callback(null, true);
		}

		// Check if the requesting origin is in our allowed list
		if (AllowedOrigins.indexOf(origin) !== -1) {
			// Allow if origin is in the list
			return callback(null, true);
		} else {
			console.warn(`CORS blocked request from origin: ${origin} for IdP Service`);
			console.warn(`Allowed origins: ${AllowedOrigins.join(', ')}`);
			return callback(new Error('This origin is not allowed by CORS.')); // Block the request
		}
	},
	// HTTP methods allowed for CORS requests (DELETE not needed in IdP)
	methods: ['GET', 'POST', 'PUT', 'OPTIONS'],

	// Headers clients are allowed to send via CORS
	allowedHeaders: [
		// Standard HTTP headers
		'Content-Type',
		'Authorization', // For API calls to IdP or token introspection
		'Accept', // Content negotiation
		'Accept-Language', // Internationalization
		'Accept-Encoding', // Compression negotiation
		'Origin', // Standard CORS header
		'Referer', // Sometimes needed for security checks
		'User-Agent', // Client identification

		// Common AJAX/API headers
		'X-Requested-With', // Common header for AJAX requests

		// Custom service headers
		'X-Service-Name', // If another service calls IdP and identifies itself
		'X-Request-ID', // For tracing requests through the system
		'X-Correlation-ID', // Alternative correlation ID for distributed tracing
		'X-Client-Version', // To track client application versions

		// Proxy/Load balancer headers
		'X-Forwarded-For', // For proxy scenarios
		'X-Real-IP', // Alternative to X-Forwarded-For

		// Caching headers
		'Cache-Control',
		'Pragma',
		'If-None-Match', // For ETag-based caching
		'If-Modified-Since' // For Last-Modified-based caching
	],
	exposedHeaders: [
		// Request tracking headers
		'X-Request-ID',
		'X-Correlation-ID',

		// Rate limiting headers
		'X-RateLimit-Limit',
		'X-RateLimit-Reset',
		'X-RateLimit-Remaining',
		'Retry-After', // For rate limiting guidance

		// Standard HTTP response headers
		'Location', // For redirect responses
		'Content-Length', // Response size information
		'Content-Range', // For partial content responses
		'ETag', // For caching
		'Last-Modified', // For caching

		// Pagination headers (useful for future list endpoints)
		'X-Total-Count', // Total number of items
		'X-Page-Count', // Total number of pages
		'Link', // Pagination links (RFC 5988)

		// Custom service headers set by EarlySecurityHeaders
		'X-Service',
		'X-API-Version',
		'X-Environment',
		'X-No-Compression', // If you want clients to know if compression was skipped

		// Security headers that clients might need to read
		'X-Frame-Options',
		'X-Content-Type-Options',
		'X-XSS-Protection',
		'X-Permitted-Cross-Domain-Policies'
	],
	optionsSuccessStatus: 200, // For legacy browser support
	credentials: true, // Crucial for IdP to handle cookies/sessions
	maxAge: 86400 // Cache preflight request results for 24 hours (in seconds)
};

//! Helmet configuration with settings appropriate for an IdP
export const HelmetConfiguration = helmet({
	contentSecurityPolicy: {
		directives: {
			defaultSrc: ["'self'"], // Only allow resources from the same origin (by default)
			scriptSrc: ["'self"], // IdP might serve some scripts for login/redirect pages
			styleSrc: ["'self", "'unsafe-inline"], // IdP might serve styles (from same origin or CDN)
			imgSrc: ["'self'", "'data:"], // Allow data URIs for small images/icons
			connectSrc: ["'self'", ...AllowedOrigins], // Allow connections to self and other allowed origins
			frameSrc: ["'none"], // IdP should generally not be framed
			objectSrc: ["'none'"], // Block <object>, <embed & <applet> elements
			baseUri: ["'self'"], // Restrict base URI
			fontSrc: ["'self'"], // Only self-hosted fonts
			manifestSrc: ["'self'"], // Web app manifests
			mediaSrc: ["'none'"], // No media needed for API
			workerSrc: ["'none'"], // No workers needed

			// Don't upgrade HTTP requests to HTTPS (enable in production)
			upgradeInsecureRequests: env_idp.ENV !== EnumEnv.Development ? [] : null
		}
	},

	// Enhanced security policies
	// Controls whether the page can embed resources from other origins
	crossOriginEmbedderPolicy: false, // Allows resources from any origin to be embedded
	//!crossOriginEmbedderPolicy: { policy: "require-corp" }, // Stricter for production

	// Controls 'window.opener' relationship between windows/tabs
	crossOriginOpenerPolicy: { policy: 'same-origin' }, // Isolation from cross-origin windows

	// Controls which origins can load the resource
	crossOriginResourcePolicy: { policy: 'same-site' }, // Allow resources to be shared with the same site

	// Controls DNS prefetching. Used to improve page load times by resolving domain names into IP addresses before those domains are actually needed
	// Consider enabling if IdP pages link to many external domains
	dnsPrefetchControl: { allow: false }, // Disabled

	// Prevents clickjacking by restricting who can put your site in a frame
	frameguard: { action: 'deny' }, // Prevent any framing of the page

	// (HTTP Strict Transport Security) - Forces HTTPS
	hsts: {
		maxAge: 31536000, // 1 year for IdP (strict)
		includeSubDomains: true, // Applied to all subdomains as well
		preload: true // Allow inclusion in browser HSTS preload list
	},

	// Controls what information is included in the Referer header
	referrerPolicy: { policy: 'strict-origin-when-cross-origin' }, // Limited referrer info for cross-origin requests

	// (Cross-Site Scripting) - Enables browser's built-in XSS protection
	xssFilter: true, // Enable XSS filtering, also for older IE browsers

	// X-Context-Type Options: Prevents MIME type sniffing
	noSniff: true, // Prevents browsers from trying to guess the content type

	// Hide server information
	// Good practice, though EarlySecurityHeaders also removes X-Powered-By
	hidePoweredBy: true
});
