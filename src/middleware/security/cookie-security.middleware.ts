import { <PERSON>rror<PERSON><PERSON>, HttpStatus, EnumEnv } from 'excelytics.shared-internals';
import type { NextFunction, Response, Request } from 'express';
import { URIConstants } from '@/constants/uri.constants';
import { env_idp } from '@app-types/env_';
import MongoStore from 'connect-mongo';
import session from 'express-session';

//! Updated with Gemini 2.5 Pro (03.06.2025)
const sessionSecret = env_idp.SESSION_SECRET as string;
const sessionMaxAgeMs = env_idp.SESSION_MAX_AGE_MS as number;

// Ensure SESSION_SECRET is set, especially for production
if (!env_idp.SESSION_SECRET && env_idp.ENV !== EnumEnv.Development) {
	console.error(`FATAL ERROR: SESSION_SECRET is not defined in the environment variables ${env_idp.ENV} environment`);
	process.exit(1);
}

//! Configure session store - use same connection logic as main DB
let sessionStore: session.Store | undefined;
let mongoUrl: string;

// Use same connection logic as db.ts
if (env_idp.VPN) {
	mongoUrl = env_idp.MONGODB_URI_UNRAID!;
} else {
	mongoUrl = env_idp.MONGODB_URI;
}

if (env_idp.ENV !== EnumEnv.Development) {
	if (!mongoUrl) {
		console.error('FATAL ERROR: MongoDB URI is not defined for session store in a non-development environment');
		process.exit(1);
	}

	sessionStore = MongoStore.create({
		mongoUrl: mongoUrl,
		collectionName: 'sessions_idp', // Explicit collection name for IdP sessions
		ttl: 14 * 24 * 60 * 60, // Optional: Session TTL in seconds (14 days)
		touchAfter: 24 * 3600, // Optional: time period in seconds to avoid unnecessary session updates
		// Add connection timeout settings
		mongoOptions: {
			serverSelectionTimeoutMS: 5000, // 5 second timeout
			connectTimeoutMS: 10000, // 10 second connection timeout
		},
		// autoRemove: 'native', // Default, uses MongoDB's TTL indexing
		// Optional: For encrypting session data in the DB
		// crypto: {
		//   secret: environments.SESSION_STORE_CRYPTO_SECRET || 'default-store-crypto-secret',
		//   algorithm: 'aes-256-gcm'
		// },
	});
	console.log('Production session store configured using MongoDB');
} else {
	console.log('Development session store configured using default MemoryStore (not suitable for production)');
}

// Configuration options for express-session middleware, tailored for the IdP
// Includes secure cookie settings and a persistent session store for production
const SessionOptions = session({
	secret: sessionSecret,
	resave: false, // Don't save session if unmodified
	store: sessionStore, // Use MongoStore in production, MemoryStore in dev
	saveUninitialized: true, // Create session even if nothing stored (needed for testing)
	name: 'excelytics.idp.sid', // More specific session cookie name
	cookie: {
		secure: false, // Disable secure for testing (HTTP)
		httpOnly: true, // Prevents client-side JS access to the cookie
		maxAge: sessionMaxAgeMs || 60 * 60 * 1000, // 1 hour default, configurable
		sameSite: 'lax', // Lax for testing compatibility
		path: '/', // Cookie is valid for all paths on the domain
		domain: undefined, // No domain restriction for testing
	},

	// Optional: Resets the cookie Max-Age on every response. Use with caution.
	// rolling: true,

	// Optional: Custom session ID generation
	// genid: function(req) { return genuuid() } // use UUIDs for session IDs
});

// Middleware to regenerate the user's session and includes robust error handling
// This is useful after login or any significant security event to prevent session fixation
const RegenerateSessionAndHandleErrors = (req: Request, res: Response, next: NextFunction) => {
	// Check if a session already exists before trying to regenerate
	if (!req.session) {
		// This middleware is used after a session is established.
		console.warn('Attempted to regenerate session, but no session exists on the request');

		// Depending on your flow, you might want to create a new session here or error out.
		// For now, let's proceed to next, assuming session middleware will create one if needed.
		return next();
	}

	// Keep old ID for logging if needed
	const oldSessionId = req.session.id;

	req.session.regenerate(err => {
		if (err) {
			console.error(`Error regenerating session (old ID: ${oldSessionId}):`, err);

			// Construct a more informative error object for the global error handler
			const sessionError: any = new Error('Failed to regenerate session. Please try logging in again');
			sessionError.statusCode = HttpStatus.INTERNAL_SERVER_ERROR; // 500
			sessionError.code = ErrorCodes.SESSION_REGENERATION_FAILED;
			sessionError.originalError = err; // Attach original error for detailed logging
			return next(sessionError);
		}

		// Optional: Copy necessary data from the old session to the new one if needed
		// For example, if you stored a 'preAuthURL' or similar:
		// if (req.session.preAuthURL) {
		//   const preAuthURL = req.session.preAuthURL;
		//   req.session.preAuthURL = preAuthURL; // This would be after regenerate, so it's on the new session
		// }
		// Be careful what you copy; usually, after login, you populate the new session with user data.

		console.log(`Session regenerated. Old ID: ${oldSessionId}, New ID: ${req.session.id}`);
		next(); // Proceed to the next middleware (e.g., setting user data on the new session)
	});
};

export const SessionMiddleware = {
	SessionOptions,
	RegenerateSessionAndHandleErrors,
};
