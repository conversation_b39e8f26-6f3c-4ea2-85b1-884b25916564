import { env_idp } from '@app-types/env_';
import mongoose from 'mongoose';

let connectionString: string;
if (env_idp.VPN) {
	connectionString = env_idp.MONGODB_URI!;
} else {
	connectionString = env_idp.MONGODB_URI_LOCAL!;
}

export const connectDB = async () => {
	try {
		await mongoose.connect(connectionString);
		console.log(`-- Connected to MongoDB in ${env_idp.ENV} mode`);
	} catch (err) {
		console.error('-- Error connecting to MongoDB:', err);
		process.exit(1);
	}
};
