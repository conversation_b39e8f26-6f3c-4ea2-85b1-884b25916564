export enum LogLevelOverrideEnum {
	OFF = 0,
	ERROR = 1,
	WARN = 2,
	INFO = 3,
	DEBUG = 4,
	TRACE = 5,
}

// Maps console method names to our LogLevel enum values
export const consoleMethodLevels: { [key: string]: LogLevelOverrideEnum } = {
	trace: LogLevelOverrideEnum.TRACE,
	debug: LogLevelOverrideEnum.DEBUG,
	info: LogLevelOverrideEnum.INFO,
	warn: LogLevelOverrideEnum.WARN,
	error: LogLevelOverrideEnum.ERROR,
	log: LogLevelOverrideEnum.INFO, // console.log usually behaves like info
};

// Helper to convert environment variable string to our LogLevel enum
export function getLogLevelFromEnv(envVar: string | undefined): LogLevelOverrideEnum {
	switch (envVar?.toUpperCase()) {
		case 'TRACE':
			return LogLevelOverrideEnum.TRACE;
		case 'DEBUG':
			return LogLevelOverrideEnum.DEBUG;
		case 'INFO':
			return LogLevelOverrideEnum.INFO;
		case 'WARN':
			return LogLevelOverrideEnum.WARN;
		case 'ERROR':
			return LogLevelOverrideEnum.ERROR;
		case 'OFF':
			return LogLevelOverrideEnum.OFF;
		default:
			return LogLevelOverrideEnum.INFO; // Default to INFO if not specified or invalid
	}
}
