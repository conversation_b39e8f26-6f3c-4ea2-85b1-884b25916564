import {
	type TokenGenerationInput,
	type RefreshTokenPayload,
	type AccessTokenPayload,
	TokenPayloadSchema,
	UnauthorizedError,
	EnumTokenType,
	EnumLogLevel,
	HttpStatus,
	ErrorCodes,
	BaseError,
} from 'excelytics.shared-internals';
import jwt, { JsonWebTokenError, TokenExpiredError } from 'jsonwebtoken';
import { env_idp } from '@app-types/env_';
import type { StringValue } from 'ms';

export class JwtService {
	private readonly accessSecret: string;
	private readonly refreshSecret: string;

	constructor() {
		this.accessSecret = env_idp.JWT_SECRET as string;
		this.refreshSecret = env_idp.REFRESH_TOKEN_SECRET as string;

		// Throw an error on startup if secrets are missing.
		if (!this.accessSecret || !this.refreshSecret) {
			throw new Error('JWT secrets are not configured in environment variables!');
		}
	}

	/** Creates a short-lived Access Token. */
	public generateAccessToken(payload: TokenGenerationInput, expiresIn: StringValue | number = '30m'): string {
		// Add issuedAt as Unix timestamp for Zod validation
		const issuedAtTimestamp = Math.floor(Date.now() / 1000);
		const signPayload = {
			...payload,
			tokenType: EnumTokenType.ACCESS,
			issuedAt: issuedAtTimestamp,
		};

		const token = jwt.sign(signPayload, this.accessSecret, { expiresIn: expiresIn });

		// Log the generated token for debugging.
		if (env_idp.TEST_LOG_LEVEL === EnumLogLevel.Info) {
			console.log(`-- Generated access token: ${token}`);
		}

		return token;
	}

	/** Creates a long-lived Refresh Token. */
	public generateRefreshToken(payload: TokenGenerationInput, expiresIn: StringValue | number = '7d'): string {
		// Explicitly remove permissions from the refresh token payload.
		const { permissions, ...basePayload } = payload;

		// Add issuedAt as Unix timestamp for Zod validation
		const issuedAtTimestamp = Math.floor(Date.now() / 1000);
		const signPayload = {
			...basePayload,
			tokenType: EnumTokenType.REFRESH,
			issuedAt: issuedAtTimestamp,
		};

		const token = jwt.sign(signPayload, this.refreshSecret, { expiresIn: expiresIn });

		// Log the generated token for debugging.
		if (env_idp.TEST_LOG_LEVEL === EnumLogLevel.Info) {
			console.log(`-- Generated refresh token: ${token}`);
		}

		return token;
	}

	/**
	 * Verifies a token internally within the IdP, and throws a specific error on failure.
	 * @param token The JWT string.
	 * @param secret The secret to use for verification.
	 * @returns The validated and parsed token payload.
	 */
	private verifyToken(token: string, secret: string): AccessTokenPayload | RefreshTokenPayload {
		try {
			// jwt.verify returns the raw decoded object.
			const decodedFromJwt = jwt.verify(token, secret) as any;

			// Transform JWT standard claims to our schema format
			const transformedPayload = {
				...decodedFromJwt,
				// Use iat as issuedAt (keep as Unix timestamp)
				issuedAt: decodedFromJwt.iat || decodedFromJwt.issuedAt,
				// Use exp as expiresAt (keep as Unix timestamp) if present
				expiresAt: decodedFromJwt.exp || undefined,
			};

			// Remove the original JWT claims to avoid confusion
			delete transformedPayload.iat;
			delete transformedPayload.exp;

			// Validate the structure and transform using Zod.
			const validationResult = TokenPayloadSchema.safeParse(transformedPayload);
			if (!validationResult.success) {
				console.error('Token failed Zod validation:', validationResult.error.format());

				// Throw a specific error instead of returning null.
				throw new UnauthorizedError('Token payload is malformed.', ErrorCodes.INVALID_TOKEN);
			}

			// validationResult.data is the fully typed payload.
			return validationResult.data;
		} catch (error) {
			// Catch specific JWT errors and re-throw as our custom errors.
			if (error instanceof TokenExpiredError) {
				throw new UnauthorizedError('Token has expired.', ErrorCodes.TOKEN_EXPIRED, error);
			}
			if (error instanceof JsonWebTokenError) {
				throw new UnauthorizedError('Token is invalid.', ErrorCodes.INVALID_TOKEN, error);
			}
			// Re-throw our own custom errors or wrap unknown ones.
			if (error instanceof BaseError) {
				throw error;
			}

			throw new BaseError(
				'An unexpected error occurred during token verification.',
				HttpStatus.INTERNAL_SERVER_ERROR,
				ErrorCodes.TOKEN_VERIFICATION_FAILED,
				undefined,
				error
			);
		}
	}

	/** Verifies an access token and ensures it is the correct type. */
	public verifyAccessTokenInternal(token: string): AccessTokenPayload {
		const payload = this.verifyToken(token, this.accessSecret);
		if (payload.tokenType !== EnumTokenType.ACCESS) {
			throw new UnauthorizedError(
				'Invalid token type provided. Expected access token.',
				ErrorCodes.INVALID_TOKEN
			);
		}

		return payload as AccessTokenPayload;
	}

	/** Verifies a refresh token and ensures it is the correct type. */
	public verifyRefreshTokenInternal(token: string): RefreshTokenPayload {
		const payload = this.verifyToken(token, this.refreshSecret);
		if (payload.tokenType !== EnumTokenType.REFRESH) {
			throw new UnauthorizedError(
				'Invalid token type provided. Expected refresh token.',
				ErrorCodes.INVALID_TOKEN
			);
		}

		return payload as RefreshTokenPayload;
	}
}
