import { Unauthorized<PERSON>rror, NotFoundError, ErrorCodes, HttpStatus, BaseError } from 'excelytics.shared-internals';
import type { IdentityServiceDataResult } from '@app-types/service.types';
import { comparePasswords, hashPassword } from '@/utils/password.utils';
import IdentityModel, { Identity } from '@/models/identity.model';

export class IdentityService {
	public async getAllUsers(): Promise<IdentityServiceDataResult<Partial<Omit<Identity, 'password'>>[]>> {
		try {
			const users = await IdentityModel.find().exec();
			const usersData = users.map(u => {
				const userObject = u.toJSON() as Identity;
				const { password, ...userData } = userObject;

				return userData;
			});

			return { success: true, data: usersData };

		} catch (error: any) {
			return {
				success: false,
				error: new BaseError(
					'Failed to fetch users',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.DATABASE_ERROR,
					undefined,
					error
				)
			};
		}
	}

	public async getUserByEmail(email: string): Promise<IdentityServiceDataResult<Partial<Omit<Identity, 'password'>> | null>> {
		try {
			const user = await IdentityModel.findOne({ email }).exec();
			if (!user) {
				return {
					success: true,
					error: new NotFoundError('User not found with the given email')
				};
			}

			const userObject = user.toJSON() as Identity;
			const { password, ...userData } = userObject;

			return { success: true, data: userData };

		} catch (error: any) {
			return {
				success: false,
				error: new BaseError(
					'Failed to fetch user by email',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.DATABASE_ERROR,
					undefined,
					error
				)
			};
		}
	}

	public async updateUserByEmail(
		email: string,
		updateData: Partial<Omit<Identity, 'password' | 'email' | 'clientId'>>
	): Promise<IdentityServiceDataResult<Partial<Omit<Identity, 'password'>> | null>> {
		try {
			const user = await IdentityModel.findOneAndUpdate({ email }, updateData, { new: true }).exec();
			if (!user) {
				return {
					success: true,
					error: new NotFoundError('User not found for update')
				};
			}

			const userObject = user.toJSON() as Identity;
			const { password, ...userData } = userObject;

			return { success: true, data: userData };

		} catch (error: any) {
			return {
				success: false,
				error: new BaseError(
					'Failed to update user',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.DATABASE_ERROR,
					undefined,
					error
				)
			};
		}
	}

	public async deleteUserByEmail(email: string): Promise<IdentityServiceDataResult<{ id: string } | null>> {
		try {
			const deletedUser = await IdentityModel.findOneAndDelete({ email }).exec();
			if (!deletedUser) {
				return {
					success: true,
					error: new NotFoundError('User not found for deletion')
				};
			}

			return { success: true, data: { id: deletedUser.id } };

		} catch (error: any) {
			return {
				success: false,
				error: new BaseError(
					'Failed to delete user by email',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.DATABASE_ERROR,
					undefined,
					error
				)
			};
		}
	}

	public async deleteUserByClientId(clientId: string): Promise<IdentityServiceDataResult<{ id: string } | null>> {
		try {
			const deletedUser = await IdentityModel.findOneAndDelete({ clientId }).exec();
			if (!deletedUser) {
				return {
					success: true,
					error: new NotFoundError('User not found for deletion by client ID')
				};
			}

			return { success: true, data: { id: deletedUser.id } };

		} catch (error: any) {
			return {
				success: false,
				error: new BaseError(
					'Failed to delete user by clientId',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.DATABASE_ERROR,
					undefined,
					error
				)
			};
		}
	}

	// --- USER SELF-SERVICE METHODS ---

	public async getUserById(userId: string): Promise<IdentityServiceDataResult<Partial<Omit<Identity, 'password'>> | null>> {
		try {
			const user = await IdentityModel.findById(userId).exec();
			if (!user) {
				return {
					success: true,
					error: new NotFoundError('User not found')
				};
			}

			const userObject = user.toJSON() as Identity;
			const { password, ...userData } = userObject;

			return { success: true, data: userData };

		} catch (error: any) {
			return {
				success: false,
				error: new BaseError(
					'Failed to fetch user by ID',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.DATABASE_ERROR,
					undefined,
					error
				)
			};
		}
	}

	public async updateUserById(userId: string, updateData: Partial<Identity>): Promise<IdentityServiceDataResult<Partial<Omit<Identity, 'password'>> | null>> {
		try {
			const updatedUser = await IdentityModel.findByIdAndUpdate(
				userId,
				{ $set: updateData },
				{ new: true, runValidators: true }
			).exec();

			if (!updatedUser) {
				return {
					success: true,
					error: new NotFoundError('User not found for update')
				};
			}

			const userObject = updatedUser.toJSON() as Identity;
			const { password, ...userData } = userObject;

			return { success: true, data: userData };

		} catch (error: any) {
			return {
				success: false,
				error: new BaseError(
					'Failed to update user',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.DATABASE_ERROR,
					undefined,
					error
				)
			};
		}
	}

	public async changeUserPassword(userId: string, currentPassword: string, newPassword: string): Promise<IdentityServiceDataResult<null>> {
		try {
			// Get user with password for verification
			const user = await IdentityModel.findById(userId).select('+password').exec();
			if (!user) {
				return {
					success: false,
					error: new NotFoundError('User not found')
				};
			}

			// Verify current password
			const isCurrentPasswordValid = await comparePasswords(currentPassword, user.password);
			if (!isCurrentPasswordValid) {
				return {
					success: false,
					error: new UnauthorizedError('Current password is incorrect')
				};
			}

			// Hash new password
			const hashedNewPassword = await hashPassword(newPassword);

			// Update password and track change
			await IdentityModel.findByIdAndUpdate(
				userId,
				{
					$set: {
						password: hashedNewPassword,
						passwordChangedAt: new Date()
					}
				}
			).exec();

			return { success: true, data: null };

		} catch (error: any) {
			return {
				success: false,
				error: new BaseError(
					'Failed to change password',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.DATABASE_ERROR,
					undefined,
					error
				)
			};
		}
	}
}