import { NotFoundError, ErrorCodes, HttpStatus, BaseError } from 'excelytics.shared-internals';
import type { IdentityServiceDataResult } from '@app-types/service.types';
import IdentityModel, { Identity } from '@/models/identity.model';

export class IdentityService {
	public async getAllUsers(): Promise<IdentityServiceDataResult<Partial<Omit<Identity, 'password'>>[]>> {
		try {
			const users = await IdentityModel.find().exec();
			const usersData = users.map(u => {
				const userObject = u.toJSON() as Identity;
				const { password, ...userData } = userObject;

				return userData;
			});

			return { success: true, data: usersData };

		} catch (error: any) {
			return {
				success: false,
				error: new BaseError(
					'Failed to fetch users',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.DATABASE_ERROR,
					undefined,
					error
				)
			};
		}
	}

	public async getUserByEmail(email: string): Promise<IdentityServiceDataResult<Partial<Omit<Identity, 'password'>> | null>> {
		try {
			const user = await IdentityModel.findOne({ email }).exec();
			if (!user) {
				return {
					success: true,
					error: new NotFoundError('User not found with the given email')
				};
			}

			const userObject = user.toJSON() as Identity;
			const { password, ...userData } = userObject;

			return { success: true, data: userData };

		} catch (error: any) {
			return {
				success: false,
				error: new BaseError(
					'Failed to fetch user by email',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.DATABASE_ERROR,
					undefined,
					error
				)
			};
		}
	}

	public async updateUserByEmail(
		email: string,
		updateData: Partial<Omit<Identity, 'password' | 'email' | 'clientId'>>
	): Promise<IdentityServiceDataResult<Partial<Omit<Identity, 'password'>> | null>> {
		try {
			const user = await IdentityModel.findOneAndUpdate({ email }, updateData, { new: true }).exec();
			if (!user) {
				return {
					success: true,
					error: new NotFoundError('User not found for update')
				};
			}

			const userObject = user.toJSON() as Identity;
			const { password, ...userData } = userObject;

			return { success: true, data: userData };

		} catch (error: any) {
			return {
				success: false,
				error: new BaseError(
					'Failed to update user',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.DATABASE_ERROR,
					undefined,
					error
				)
			};
		}
	}

	public async deleteUserByEmail(email: string): Promise<IdentityServiceDataResult<{ id: string } | null>> {
		try {
			const deletedUser = await IdentityModel.findOneAndDelete({ email }).exec();
			if (!deletedUser) {
				return {
					success: true,
					error: new NotFoundError('User not found for deletion')
				};
			}

			return { success: true, data: { id: deletedUser.id } };

		} catch (error: any) {
			return {
				success: false,
				error: new BaseError(
					'Failed to delete user by email',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.DATABASE_ERROR,
					undefined,
					error
				)
			};
		}
	}

	public async deleteUserByClientId(clientId: string): Promise<IdentityServiceDataResult<{ id: string } | null>> {
		try {
			const deletedUser = await IdentityModel.findOneAndDelete({ clientId }).exec();
			if (!deletedUser) {
				return {
					success: true,
					error: new NotFoundError('User not found for deletion by client ID')
				};
			}

			return { success: true, data: { id: deletedUser.id } };

		} catch (error: any) {
			return {
				success: false,
				error: new BaseError(
					'Failed to delete user by clientId',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.DATABASE_ERROR,
					undefined,
					error
				)
			};
		}
	}
}