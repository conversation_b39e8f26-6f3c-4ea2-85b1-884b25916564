import {
	type TokenGenerationInput,
	type TokenPayload,
	UnauthorizedError,
	EnumClientPath,
	ForbiddenError,
	ConflictError,
	EnumLogLevel,
	ErrorCodes,
	HttpStatus,
	BaseError,
	UserRoles
} from 'excelytics.shared-internals';
import type {
	ExcelyticsServiceResultRefined,
	RefreshTokenServiceResult,
	RegistrationServiceResult,
	LoginServiceResult
} from '@app-types/service.types';
import IdentityModel, { type Identity } from '@/models/identity.model';
import type { RegisterDTO, LoginDTO } from '@/utils/validation.util';
import { comparePasswords } from '@/utils/password.utils';
import { JwtService } from '@/services/jwt.service';
import { env_idp } from '@app-types/env_';
import type { StringValue } from 'ms';

const ACCESS_TOKEN_EXPIRY = env_idp.ACCESS_TOKEN_EXPIRY as StringValue;
const REFRESH_TOKEN_EXPIRY = env_idp.REFRESH_TOKEN_EXPIRY as StringValue;

export class AuthenticationService {
	private readonly jwtService: JwtService;

	constructor() {
		this.jwtService = new JwtService();
	}

	public async registerUser(data: RegisterDTO): Promise<RegistrationServiceResult> {
		try {
			const existingUser = await IdentityModel.findOne({ email: data.email });
			if (existingUser) {
				return {
					success: false,
					error: new ConflictError('User with this email already exists.')
				};
			}

			// Password already hashed by controller
			const newUser = await IdentityModel.create(data);

			const { ...userWithoutPassword } = newUser.toJSON() as Omit<Identity, 'password'>;
			return { success: true, data: { user: userWithoutPassword } };
		} catch (error: any) {
			console.error('Error in registerUser service:', error);
			return {
				success: false,
				error: new BaseError(
					'User registration failed due to a server error.',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.REGISTRATION_FAILED,
					undefined,
					error
				)
			};
		}
	}

	public async loginUser(credentials: LoginDTO): Promise<LoginServiceResult> {
		try {
			const user = await IdentityModel.findOne({ email: credentials.email })
				.select('+password')
				.exec();

			// Ensure user exists and is active
			if (!user) {
				return { success: false, error: new UnauthorizedError('Invalid email or password') };
			}
			if (!user.isActive) {
				return { success: false, error: new ForbiddenError('User account is inactive') };
			}

			// Ensure password is correct
			if (!(await comparePasswords(credentials.password, user.password))) {
				return { success: false, error: new UnauthorizedError('Invalid email or password') };
			}

			// Update last login timestamp
			user.lastLogin = new Date();
			await user.save();

			// Generate JWT token with tenant information
			const tokenGenerationPayload: TokenGenerationInput = {
				userId: user.id,
				email: user.email,
				clientId: user.clientId,
				clientOrigin: user.clientOrigin,
				clientPath: credentials.clientPath || EnumClientPath.Identity,
				isActive: user.isActive,
				permissions: user.roles || [UserRoles.USER] // Use roles from database
			};

			const newAccessToken = this.jwtService.generateAccessToken(tokenGenerationPayload, ACCESS_TOKEN_EXPIRY);
			const newRefreshToken = this.jwtService.generateRefreshToken(tokenGenerationPayload, REFRESH_TOKEN_EXPIRY);

			// Since verifyAccessTokenInternal now throws on error, we wrap it.
			// A token we *just* created should never fail verification. If it does, it's a critical internal error.
			try {
				const newDecodedAccessToken = this.jwtService.verifyAccessTokenInternal(newAccessToken);

				const { ...userWithoutPassword } = user.toJSON() as Omit<Identity, 'password'>;
				return {
					success: true,
					data: {
						accessToken: newAccessToken,
						refreshToken: newRefreshToken,
						user: userWithoutPassword,
						tokenPayload: newDecodedAccessToken
					}
				};
			} catch (verificationError) {
				console.error('[CRITICAL] Newly generated token failed verification.', verificationError);
				return {
					success: false,
					error: new BaseError(
						'Failed to generate valid access token',
						HttpStatus.INTERNAL_SERVER_ERROR,
						ErrorCodes.TOKEN_GENERATION_ERROR,
						undefined,
						verificationError
					)
				};
			}
		} catch (error: any) {
			console.error('Error in loginUser service:', error);
			if (error instanceof BaseError) {
				return { success: false, error };
			}

			return {
				success: false,
				error: new BaseError(
					'Login process failed.',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.AUTHENTICATION_FAILED,
					undefined,
					error
				)
			};
		}
	}

	public async refreshAccessToken(clientRefreshToken: string): Promise<RefreshTokenServiceResult> {
		try {
			const decodedRefreshToken = this.jwtService.verifyRefreshTokenInternal(clientRefreshToken);

			// Check if refresh token is in a deny-list/revoked list in DB (optional)
			const user = await IdentityModel.findById(decodedRefreshToken.userId);
			if (!user || !user.isActive) {
				return { success: false, error: new UnauthorizedError('User for refresh token not found or inactive') };
			}

			// Include current user roles/permissions in refreshed token
			const tokenGenerationPayload: TokenGenerationInput = {
				userId: user.id,
				email: user.email,
				clientId: user.clientId,
				clientOrigin: user.clientOrigin,
				clientPath: decodedRefreshToken.clientPath || EnumClientPath.Identity,
				isActive: user.isActive,
				permissions: user.roles || [UserRoles.USER] // Use current roles from database
			};

			const newAccessToken = this.jwtService.generateAccessToken(tokenGenerationPayload);

			// Example: If implementing refresh token rotation
			// const newRotatedRefreshToken = generateRefreshToken(newAccessTokenInputPayload);
			// await saveNewRefreshTokenAndRevokeOld(clientRefreshToken, newRotatedRefreshToken, user.id);

			const newDecodedAccessToken = this.jwtService.verifyAccessTokenInternal(newAccessToken);
			return {
				success: true,
				data: {
					accessToken: newAccessToken,
					tokenPayload: newDecodedAccessToken
					// If you implement token rotation, you would generate and add a new refresh token here as well.
				}
			};
		} catch (error: any) {
			// Only log detailed errors for unexpected errors, not expected UnauthorizedErrors
			if (error instanceof UnauthorizedError) {
				// For expected auth errors, log minimal info in test environment
				if (env_idp.LOG_LEVEL === EnumLogLevel.Info) {
					console.log(`Token refresh failed: ${error.message}`);
				} else {
					console.warn('Token refresh failed (expected):', error.message);
				}
				return { success: false, error };
			}

			// For unexpected errors, log full details
			console.error('Error in refreshAccessToken service:', error);

			return {
				success: false,
				error: new BaseError(
					'Token refresh failed',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.TOKEN_REFRESH_FAILED,
					undefined,
					error
				)
			};
		}
	}

	// "Token Introspection" is the standard industry term for the process of querying an authorization server
	// (in this case, your IdP) to determine the active state and metadata of a given token.
	public async introspectToken(token: string): Promise<ExcelyticsServiceResultRefined> {
		try {
			// This can throw if the token is expired, malformed, etc.
			const decodedPayload: TokenPayload | null = this.jwtService.verifyAccessTokenInternal(token);

			const user = await IdentityModel.findById(decodedPayload.userId)
				.select('isActive')
				.lean();

			// If user is not found or not active, the token is effectively inactive.
			if (!user || !user.isActive) {
				return { success: true, data: { active: false, claims: decodedPayload } };
			}

			// If we reach here, the token is valid and the user is active.
			return { success: true, data: { active: true, claims: decodedPayload } };

		} catch (error: any) {
			// For introspection, a token that fails verification (e.g., expired) is simply considered "inactive"
			// This is not a server error
			if (error instanceof UnauthorizedError) {
				// For expected auth errors during introspection, minimal logging
				if (env_idp.ENV === 'test') {
					// No logging for expected token validation failures in tests
				} else if (env_idp.ENV === 'development') {
					console.log(`Token introspection: ${error.message}`);
				}
				return { success: true, data: { active: false } };
			}

			// However, if another type of error occurs, it's a service failure
			console.error('Error during token introspection service:', error);
			return {
				success: false,
				error: new BaseError(
					'Token introspection process failed',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.INTROSPECTION_FAILED,
					undefined,
					error
				)
			};
		}
	}
}