/**
 * Enhanced Token Analysis Utilities
 * Provides methods for decoding and analyzing JWT tokens without verification (for internal use)
 * Supports both raw JWT strings and parsed AccessTokenPayload from Express Request
 * @module token-analysis.utils
 */

import type { AccessTokenPayload } from 'excelytics.shared-internals';
import type { Request } from 'express';

/**
 * Interface for decoded JWT token structure
 */
export interface DecodedJwtToken {
	header: {
		alg: string;
		typ: string;
		[key: string]: any;
	};
	payload: {
		[key: string]: any;
	};
	signature: string;
}

/**
 * Interface for token analysis results
 */
export interface TokenAnalysis {
	isValid: boolean;
	header?: any;
	payload?: any;
	userId?: string;
	email?: string;
	clientId?: string;
	clientOrigin?: number;
	clientPath?: string;
	tokenType?: string;
	issuedAt?: number;
	expiresAt?: number;
	remainingSeconds?: number;
	remainingMinutes?: number;
	isExpired?: boolean;
	error?: string;
}

/**
 * Decodes a JWT token without verification (for internal analysis only)
 * @param token - The JWT token string
 * @returns Decoded token structure or null if invalid
 */
export const decodeJwtToken = (token: string): DecodedJwtToken | null => {
	try {
		const parts = token.split('.');
		if (parts.length !== 3) {
			return null;
		}

		const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
		const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
		const signature = parts[2];

		return {
			header,
			payload,
			signature
		};
	} catch (error) {
		console.error('Failed to decode JWT token:', error);
		return null;
	}
};

/**
 * Analyzes a JWT token and extracts all relevant information
 * @param token - The JWT token string
 * @returns Comprehensive token analysis
 */
export const analyzeToken = (token: string): TokenAnalysis => {
	try {
		const decoded = decodeJwtToken(token);

		if (!decoded) {
			return {
				isValid: false,
				error: 'Invalid JWT token format'
			};
		}

		const { header, payload } = decoded;
		const now = Math.floor(Date.now() / 1000);

		// Calculate remaining time
		const expiresAt = payload.exp;
		const remainingSeconds = expiresAt ? Math.max(0, expiresAt - now) : 0;
		const remainingMinutes = Math.floor(remainingSeconds / 60);
		const isExpired = expiresAt ? now >= expiresAt : false;

		return {
			isValid: true,
			header,
			payload,
			userId: payload.userId || payload.sub,
			email: payload.email,
			clientId: payload.clientId || payload.client_id,
			clientOrigin: payload.clientOrigin,
			clientPath: payload.clientPath,
			tokenType: payload.tokenType || payload.token_type,
			issuedAt: payload.iat || payload.issuedAt,
			expiresAt: payload.exp,
			remainingSeconds,
			remainingMinutes,
			isExpired
		};
	} catch (error) {
		return {
			isValid: false,
			error: `Token analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
		};
	}
};

/**
 * Logs comprehensive token information for debugging
 * @param token - The JWT token string
 * @param label - Optional label for the log output
 */
export const logTokenAnalysis = (token: string, label: string = 'Token'): void => {
	console.log(`\n🔍 === ${label.toUpperCase()} ANALYSIS ===`);

	const analysis = analyzeToken(token);
	if (!analysis.isValid) {
		console.log('❌ Invalid token:', analysis.error);
		return;
	}

	console.log('✅ Token is valid');
	console.log('📄 Header:', analysis.header);
	console.log('📦 Payload:', analysis.payload);

	console.log('\n👤 User Information:');
	console.log('   User ID:', analysis.userId);
	console.log('   Email:', analysis.email);
	console.log('   Client ID:', analysis.clientId);
	console.log('   Client Origin:', analysis.clientOrigin);
	console.log('   Client Path:', analysis.clientPath);

	console.log('\n🎫 Token Information:');
	console.log('   Type:', analysis.tokenType);
	console.log(
		'   Issued At:',
		analysis.issuedAt ? new Date(analysis.issuedAt * 1000).toISOString() : 'unknown'
	);
	console.log(
		'   Expires At:',
		analysis.expiresAt ? new Date(analysis.expiresAt * 1000).toISOString() : 'never'
	);
	console.log(
		'   Remaining Time:',
		`${analysis.remainingMinutes} minutes (${analysis.remainingSeconds} seconds)`
	);
	console.log('   Is Expired:', analysis.isExpired ? '❌ Yes' : '✅ No');

	console.log(`=== END ${label.toUpperCase()} ANALYSIS ===\n`);
};

/**
 * Extracts user information from a token for logging/debugging
 * @param token - The JWT token string
 * @returns User information object
 */
export const extractUserInfo = (
	token: string
): {
	userId?: string;
	email?: string;
	clientId?: string;
	clientOrigin?: number;
	isValid: boolean;
} => {
	const analysis = analyzeToken(token);

	return {
		userId: analysis.userId,
		email: analysis.email,
		clientId: analysis.clientId,
		clientOrigin: analysis.clientOrigin,
		isValid: analysis.isValid
	};
};

/**
 * Compares two tokens and shows differences
 * @param token1 - First token
 * @param token2 - Second token
 * @param label1 - Label for first token
 * @param label2 - Label for second token
 */
export const compareTokens = (
	token1: string,
	token2: string,
	label1: string = 'Token 1',
	label2: string = 'Token 2'
): void => {
	console.log(`\n🔄 === COMPARING ${label1.toUpperCase()} vs ${label2.toUpperCase()} ===`);

	const analysis1 = analyzeToken(token1);
	const analysis2 = analyzeToken(token2);

	console.log(
		'Validity:',
		analysis1.isValid ? '✅' : '❌',
		'vs',
		analysis2.isValid ? '✅' : '❌'
	);
	console.log('User ID:', analysis1.userId, 'vs', analysis2.userId);
	console.log('Email:', analysis1.email, 'vs', analysis2.email);
	console.log('Token Type:', analysis1.tokenType, 'vs', analysis2.tokenType);
	console.log(
		'Remaining Time:',
		`${analysis1.remainingMinutes}m vs ${analysis2.remainingMinutes}m`
	);

	console.log('=== END COMPARISON ===\n');
};

// ===== EXPRESS REQUEST UTILITIES =====

/**
 * Extracts user information from Express Request.user (AccessTokenPayload)
 * @param req - Express Request object
 * @returns User information object
 */
export const extractUserFromRequest = (
	req: Request
): {
	userId?: string;
	email?: string;
	clientId?: string;
	clientOrigin?: number;
	clientPath?: string;
	tokenType?: string;
	isActive?: boolean;
	isValid: boolean;
} => {
	const user = req.user as AccessTokenPayload;

	if (!user) {
		return { isValid: false };
	}

	return {
		userId: user.userId,
		email: user.email,
		clientId: user.clientId,
		clientOrigin: user.clientOrigin,
		clientPath: user.clientPath,
		tokenType: user.tokenType,
		isActive: user.isActive,
		isValid: true
	};
};

/**
 * Logs user information from Express Request.user
 * @param req - Express Request object
 * @param label - Optional label for the log output
 */
export const logUserFromRequest = (req: Request, label: string = 'Request User'): void => {
	console.log(`\n👤 === ${label.toUpperCase()} ANALYSIS ===`);

	const userInfo = extractUserFromRequest(req);

	if (!userInfo.isValid) {
		console.log('❌ No user found in request');
		return;
	}

	console.log('✅ User found in request');
	console.log('👤 User Information:');
	console.log('   User ID:', userInfo.userId);
	console.log('   Email:', userInfo.email);
	console.log('   Client ID:', userInfo.clientId);
	console.log('   Client Origin:', userInfo.clientOrigin);
	console.log('   Client Path:', userInfo.clientPath);
	console.log('   Token Type:', userInfo.tokenType);
	console.log('   Is Active:', userInfo.isActive ? '✅ Yes' : '❌ No');

	console.log(`=== END ${label.toUpperCase()} ANALYSIS ===\n`);
};

/**
 * Analyzes AccessTokenPayload from Express Request.user
 * @param req - Express Request object
 * @returns Comprehensive user analysis
 */
export const analyzeUserFromRequest = (
	req: Request
): {
	isValid: boolean;
	user?: AccessTokenPayload;
	userId?: string;
	email?: string;
	clientId?: string;
	clientOrigin?: number;
	clientPath?: string;
	tokenType?: string;
	isActive?: boolean;
	hasPermissions?: boolean;
	permissions?: string[];
	error?: string;
} => {
	try {
		const user = req.user as AccessTokenPayload;

		if (!user) {
			return {
				isValid: false,
				error: 'No user found in request'
			};
		}

		return {
			isValid: true,
			user,
			userId: user.userId,
			email: user.email,
			clientId: user.clientId,
			clientOrigin: user.clientOrigin,
			clientPath: user.clientPath,
			tokenType: user.tokenType,
			isActive: user.isActive,
			hasPermissions: !!(user as any).permissions,
			permissions: (user as any).permissions || []
		};
	} catch (error) {
		return {
			isValid: false,
			error: `User analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
		};
	}
};

/**
 * Checks if the user in the request has a specific permission
 * @param req - Express Request object
 * @param permission - Permission to check for
 * @returns Boolean indicating if user has permission
 */
export const userHasPermission = (req: Request, permission: string): boolean => {
	const user = req.user as any;

	if (!user || !user.permissions) {
		return false;
	}

	return user.permissions.includes(permission);
};

/**
 * Gets user ID from request (convenience function)
 * @param req - Express Request object
 * @returns User ID or null
 */
export const getUserId = (req: Request): string | null => {
	const user = req.user as AccessTokenPayload;
	return user?.userId || null;
};

/**
 * Gets user email from request (convenience function)
 * @param req - Express Request object
 * @returns User email or null
 */
export const getUserEmail = (req: Request): string | null => {
	const user = req.user as AccessTokenPayload;
	return user?.email || null;
};

/**
 * Gets client ID from request (convenience function)
 * @param req - Express Request object
 * @returns Client ID or null
 */
export const getClientId = (req: Request): string | null => {
	const user = req.user as AccessTokenPayload;
	return user?.clientId || null;
};