import {
	ObjectIdStringSchema,
	EnumClientOrigin,
	EnumClientPath
} from 'excelytics.shared-internals';
import { z } from 'zod';

const BaseSchema = z.object({
	email: z.string().email('Invalid email format'),
	clientPath: z.nativeEnum(EnumClientPath).optional(),
	permissions: z.array(z.string()).optional()
});

export const RegisterSchema = BaseSchema.extend({
	// This will be the user's Id (From Users table on Finance instance)
	clientId: ObjectIdStringSchema,
	clientOrigin: z.nativeEnum(EnumClientOrigin),
	isActive: z.boolean().default(true),
	password: z
		.string()
		.min(8, 'Password must be at least 8 characters long')
		.regex(/[0-9]/, 'Password must contain at least one number')
		.regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
		.regex(/[a-z]/, 'Password must contain at least one lowercase letter')
		.regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character')
});

export const LoginSchema = BaseSchema.extend({
	password: z.string().min(8, 'Password must be at least 8 characters long')
});

export type RegisterDTO = z.infer<typeof RegisterSchema>;
export type LoginDTO = z.infer<typeof LoginSchema>;