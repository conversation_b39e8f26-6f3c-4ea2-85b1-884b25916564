import bcrypt from 'bcryptjs';

const SALT_ROUNDS = 10;

export async function hashPassword(password: string): Promise<string> {
	try {
		return await bcrypt.hash(password, SALT_ROUNDS);
	} catch (err) {
		console.error(`-- Error hashing password: ${err}`);
		throw err;
	}
}

export async function comparePasswords(
	plainTextPassword: string,
	hashedPassword?: string
): Promise<boolean> {
	if (!hashedPassword) {
		return false;
	}

	return await bcrypt.compare(plainTextPassword, hashedPassword);
}