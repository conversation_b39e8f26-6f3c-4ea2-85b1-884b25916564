import { createHealthCheckHandler } from 'excelytics.shared-internals';
import { healthHelper } from '@/services/health.service';
import asyncHandler from 'express-async-handler';
import { Router } from 'express';

const router = Router();

// Create the specific handlers using the factory and the configured helper
const shallowHealthHandler = createHealthCheckHandler(healthHelper, false);
const deepHealthHandler = createHealthCheckHandler(healthHelper, true);

// --- Health Check Endpoints ---
router.get('/health', asyncHandler(shallowHealthHandler));
router.get('/health/all', asyncHandler(deepHealthHandler));

export default router;
