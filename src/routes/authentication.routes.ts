import { AuthenticationController } from '@/controllers/authentication.controller';
import { UtilityMiddleware } from '@/middleware/utility.middleware';
import asyncHandler from 'express-async-handler';
import { Router } from 'express';

const router = Router();
const utilityMiddleware = new UtilityMiddleware();
const authController = new AuthenticationController();

/*
Note, we are using asyncHandler to prevent the need for try-catch blocks in route handlers
 In essence, as<PERSON><PERSON><PERSON><PERSON> simplifies error handling for asynchronous route handlers by ensuring
 that any unhandled promise rejections are correctly propagated to Express's error handling mechanism.

bind():
 Creates a new function that, when called, calls this function with its this keyword set to the provided value,
  and a given sequence of arguments preceding any provided when the new function is called.
*/

// --- Standard Authentication Endpoints ---
router.post('/login', asyncHandler(authController.login.bind(authController)));
router.post('/register', asyncHandler(authController.register.bind(authController)));
router.post('/refresh-token', asyncHandler(authController.refreshToken.bind(authController)));

// --- Token Introspection Endpoint (OAuth2 Style - RFC 7662) ---
// This endpoint is called by other services (Resource Servers) to validate a token issued by this IdP
router.post(
	'/introspect', // Standard path for token introspection
	asyncHandler(utilityMiddleware.introspectionEndpointHandler.bind(utilityMiddleware))
);

// Session endpoint for session management and testing
router.get('/session', asyncHandler(authController.getSession.bind(authController)));

// Test endpoint for generating short-lived tokens (development/test only)
router.post('/generate-test-token', asyncHandler(authController.generateTestToken.bind(authController)));

export default router;
