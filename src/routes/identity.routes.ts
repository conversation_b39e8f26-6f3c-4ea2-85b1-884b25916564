import {
	validateRequestParams,
	validationSchema
} from '@/middleware/authentication/validation.middleware';
import {
	requireAdminRole,
	requireAuth
} from '@/middleware/authentication/authentication.middleware';
import { IdentityController } from '@/controllers/identity.controller';
import asyncHand<PERSON> from 'express-async-handler';
import { Router } from 'express';

const router = Router();
const identityController = new IdentityController();

// --- SECURITY MIDDLEWARE ---
// 1. First, verify the user is authenticated with a valid token.
router.use(asyncHandler(requireAuth));

// 2. Second, verify the user has the 'admin' role for all identity management endpoints.
router.use(asyncHandler(requireAdminRole));

// --- ROUTES ---
// GET Endpoints
router.get(
	'/users',
	asyncHandler(identityController.getUsers.bind(identityController))
);
router.get(
	'/user/:email',
	validateRequestParams(validationSchema.emailParam),
	async<PERSON><PERSON><PERSON>(identityController.getUserByEmail.bind(identityController))
);

// UPDATE Endpoints
router.put(
	'/user/:email',
	validateRequestParams(validationSchema.emailParam),
	asyncHandler(identityController.updateUserByEmail.bind(identityController))
);

// DELETE Endpoints
router.delete(
	'/email/:email',
	validateRequestParams(validationSchema.emailParam),
	asyncHandler(identityController.deleteUserByEmail.bind(identityController))
);
router.delete(
	'/clientId/:clientId',
	validateRequestParams(validationSchema.clientIdParam),
	asyncHandler(identityController.deleteUserByClientId.bind(identityController))
);

export default router;