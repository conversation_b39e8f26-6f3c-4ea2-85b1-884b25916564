import authenticationRoutes from '@/routes/authentication.routes';
import utilityRoutes from '@/routes/utility.routes';
import healthRoutes from '@/routes/health.routes';
import userRoutes from '@/routes/identity.routes';
import { env_idp } from '@app-types/env_';
import express from 'express';

const BASE_PATH = `/api/${env_idp.API_VERSION}`;

const SetupRoutes = (app: express.Application) => {
	// User management routes (e.g., /api/v1/identity/users/:email)
	app.use(`${BASE_PATH}/identity`, userRoutes);

	// Auth Routes (e.g., /api/v1/auth/login, /api/v1/auth/register)
	app.use(`${BASE_PATH}/auth`, authenticationRoutes);

	// Health Check & Utility Routes (e.g., /api/v1/health, /api/v1/verify-token)
	app.use(`${BASE_PATH}/`, healthRoutes);
	app.use(`${BASE_PATH}/`, utilityRoutes);
};

export default SetupRoutes;
