import {
	CustomSuccessResponse,
	CustomErrorResponse,
	EnumClientOrigin,
	EnumClientPath,
	HttpStatus,
	ErrorCodes,
	BaseError
} from 'excelytics.shared-internals';
import type { AuthenticatedRequest } from '@/interfaces/auth.interface';
import type { NextFunction, Response, Request } from 'express';
import { IdentityService } from '@/services/identity.service';
import type { Identity } from '@/models/identity.model';
import { z } from 'zod';

const UpdateUserPayloadSchema = z
	.object({
		isActive: z.boolean().optional(),
		clientOrigin: z.nativeEnum(EnumClientOrigin).optional(),
		clientPath: z.nativeEnum(EnumClientPath).optional()
	})
	.strict();

const UpdateCurrentUserSchema = z
	.object({
		// Users can only update limited fields from Identity model
		emailVerified: z.boolean().optional()
		// Add other safe fields users can update themselves
	})
	.strict();

const ChangePasswordSchema = z
	.object({
		currentPassword: z.string().min(1, 'Current password is required'),
		newPassword: z
			.string()
			.min(8, 'Password must be at least 8 characters long')
			.regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
			.regex(/[a-z]/, 'Password must contain at least one lowercase letter')
			.regex(/[0-9]/, 'Password must contain at least one number')
			.regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character')
	})
	.strict();

export class IdentityController {
	private readonly identityService: IdentityService;
	private readonly errorResponder: CustomErrorResponse;
	private readonly successResponder: CustomSuccessResponse;

	constructor() {
		this.identityService = new IdentityService();
		this.errorResponder = new CustomErrorResponse();
		this.successResponder = new CustomSuccessResponse();
	}

	public async getUsers(request: Request, response: Response, _: NextFunction): Promise<void> {
		const result = await this.identityService.getAllUsers();
		if (!result.success || !result.data) {
			this.errorResponder.SendError(
				response,
				result.error ||
					new BaseError(
						'Failed to get users',
						HttpStatus.INTERNAL_SERVER_ERROR,
						ErrorCodes.FETCH_FAILED
					)
			);
			return;
		}

		this.successResponder.SendSuccessResponse(
			response,
			result.data.length > 0 ? 'Users retrieved' : 'No users found',
			result.data
		);
	}

	public async getUserByEmail(
		request: Request,
		response: Response,
		_: NextFunction
	): Promise<void> {
		const { email } = request.params;

		const result = await this.identityService.getUserByEmail(email);
		if (!result.success) {
			this.errorResponder.SendError(
				response,
				result.error ||
					new BaseError(
						'Failed to get user',
						HttpStatus.INTERNAL_SERVER_ERROR,
						ErrorCodes.FETCH_FAILED
					)
			);
			return;
		}

		if (!result.data) {
			this.errorResponder.SendNotFoundError(response, 'User not found with the given email.');
			return;
		}

		this.successResponder.SendSuccessResponse(response, 'User retrieved.', result.data);
	}

	public async updateUserByEmail(
		request: Request,
		response: Response,
		_: NextFunction
	): Promise<void> {
		// Param validation is handled by middleware.
		const { email } = request.params;

		const validationResult = UpdateUserPayloadSchema.safeParse(request.body);
		if (!validationResult.success) {
			this.errorResponder.SendValidationError(
				response,
				validationResult.error,
				'Invalid update data.'
			);
			return;
		}

		const updateData = validationResult.data as Partial<
			Omit<Identity, 'password' | 'email' | 'clientId'>
		>;

		const result = await this.identityService.updateUserByEmail(email, updateData);
		if (!result.success) {
			this.errorResponder.SendError(
				response,
				result.error ||
					new BaseError(
						'Failed to update user',
						HttpStatus.INTERNAL_SERVER_ERROR,
						ErrorCodes.UPDATE_FAILED
					)
			);
			return;
		}

		if (!result.data) {
			this.errorResponder.SendNotFoundError(response, 'User not found for update.');
			return;
		}

		this.successResponder.SendSuccessResponse(response, 'User updated.', result.data);
	}

	public async deleteUserByEmail(
		request: Request,
		response: Response,
		_: NextFunction
	): Promise<void> {
		const { email } = request.params;

		const result = await this.identityService.deleteUserByEmail(email);
		if (!result.success) {
			this.errorResponder.SendError(
				response,
				result.error ||
					new BaseError(
						'Failed to delete user',
						HttpStatus.INTERNAL_SERVER_ERROR,
						ErrorCodes.DELETE_FAILED
					)
			);
			return;
		}

		if (!result.data) {
			this.errorResponder.SendNotFoundError(response, 'User not found for deletion.');
			return;
		}

		this.successResponder.SendSuccessResponse(response, 'User deleted.', result.data);
	}

	public async deleteUserByClientId(
		request: Request,
		response: Response,
		_: NextFunction
	): Promise<void> {
		// Param validation is handled by middleware.
		const { clientId } = request.params;

		const result = await this.identityService.deleteUserByClientId(clientId);
		if (!result.success) {
			this.errorResponder.SendError(
				response,
				result.error ||
					new BaseError(
						'Failed to delete user by client ID',
						HttpStatus.INTERNAL_SERVER_ERROR,
						ErrorCodes.DELETE_FAILED
					)
			);
			return;
		}

		if (!result.data) {
			this.errorResponder.SendNotFoundError(
				response,
				'User not found for deletion by client ID.'
			);
			return;
		}

		this.successResponder.SendSuccessResponse(
			response,
			'User deleted by client ID.',
			result.data
		);
	}

	// --- USER SELF-SERVICE METHODS ---

	/**
	 * Get current user's profile
	 */
	public async getCurrentUser(
		request: AuthenticatedRequest,
		response: Response,
		_: NextFunction
	): Promise<void> {
		try {
			if (!request.user?.userId) {
				this.errorResponder.SendUnauthorizedError(response, 'User not authenticated');
				return;
			}

			const result = await this.identityService.getUserById(request.user.userId);
			if (!result.success || !result.data) {
				this.errorResponder.SendError(
					response,
					result.error ||
						new BaseError(
							'Failed to get current user',
							HttpStatus.INTERNAL_SERVER_ERROR,
							ErrorCodes.FETCH_FAILED
						)
				);
				return;
			}

			this.successResponder.SendSuccessResponse(
				response,
				'Current user retrieved.',
				result.data
			);
		} catch (error) {
			this.errorResponder.SendError(
				response,
				new BaseError(
					'Failed to get current user',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.FETCH_FAILED
				)
			);
		}
	}

	/**
	 * Update current user's profile (limited fields)
	 */
	public async updateCurrentUser(
		request: AuthenticatedRequest,
		response: Response,
		_: NextFunction
	): Promise<void> {
		try {
			if (!request.user?.userId) {
				this.errorResponder.SendUnauthorizedError(response, 'User not authenticated');
				return;
			}

			const validationResult = UpdateCurrentUserSchema.safeParse(request.body);
			if (!validationResult.success) {
				this.errorResponder.SendValidationError(
					response,
					validationResult.error,
					'Invalid update data.'
				);
				return;
			}

			const updateData = validationResult.data;
			const result = await this.identityService.updateUserById(
				request.user.userId,
				updateData
			);

			if (!result.success || !result.data) {
				this.errorResponder.SendError(
					response,
					result.error ||
						new BaseError(
							'Failed to update profile',
							HttpStatus.INTERNAL_SERVER_ERROR,
							ErrorCodes.UPDATE_FAILED
						)
				);
				return;
			}

			this.successResponder.SendSuccessResponse(
				response,
				'Profile updated successfully.',
				result.data
			);
		} catch (error) {
			this.errorResponder.SendError(
				response,
				new BaseError(
					'Failed to update profile',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.UPDATE_FAILED
				)
			);
		}
	}

	/**
	 * Change current user's password
	 */
	public async changePassword(
		request: AuthenticatedRequest,
		response: Response,
		_: NextFunction
	): Promise<void> {
		try {
			if (!request.user?.userId) {
				this.errorResponder.SendUnauthorizedError(response, 'User not authenticated');
				return;
			}

			const validationResult = ChangePasswordSchema.safeParse(request.body);
			if (!validationResult.success) {
				this.errorResponder.SendValidationError(
					response,
					validationResult.error,
					'Invalid password data.'
				);
				return;
			}

			const { currentPassword, newPassword } = validationResult.data;
			const result = await this.identityService.changeUserPassword(
				request.user.userId,
				currentPassword,
				newPassword
			);

			if (!result.success) {
				this.errorResponder.SendError(
					response,
					result.error ||
						new BaseError(
							'Failed to change password',
							HttpStatus.BAD_REQUEST,
							ErrorCodes.UPDATE_FAILED
						)
				);
				return;
			}

			this.successResponder.SendSuccessResponse(
				response,
				'Password changed successfully.',
				null
			);
		} catch (error) {
			this.errorResponder.SendError(
				response,
				new BaseError(
					'Failed to change password',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.UPDATE_FAILED
				)
			);
		}
	}
}
