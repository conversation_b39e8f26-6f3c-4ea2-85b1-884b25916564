/**
 * Examples of how to use the enhanced token analysis utilities
 * with Express Request.user (AccessTokenPayload)
 */

import {
	extractUserFromRequest,
	analyzeUserFromRequest,
	logUserFromRequest,
	userHasPermission,
	logTokenAnalysis,
	getUserEmail,
	analyzeToken,
	getClientId,
	getUserId
} from '../utils/token-analysis.utils';
import type { NextFunction, Response, Request } from 'express';

// ===== MIDDLEWARE EXAMPLES =====

/**
 * Example middleware that logs user information from request
 */
export const logUserMiddleware = (req: Request, res: Response, next: NextFunction) => {
	// Log user information from req.user (AccessTokenPayload)
	logUserFromRequest(req, 'Authenticated User');

	// Extract specific user info
	const userInfo = extractUserFromRequest(req);
	if (userInfo.isValid) {
		console.log(`🎯 Processing request for user: ${userInfo.email} (ID: ${userInfo.userId})`);
	}

	next();
};

/**
 * Example middleware that checks for admin permissions
 */
export const requireAdminMiddleware = (req: Request, res: Response, next: NextFunction) => {
	const hasAdminPermission = userHasPermission(req, 'admin');

	if (!hasAdminPermission) {
		return res.status(403).json({
			success: false,
			message: 'Admin permissions required'
		});
	}

	console.log('✅ Admin permission verified for user:', getUserEmail(req));
	next();
};

/**
 * Example middleware that validates client access
 */
export const validateClientMiddleware = (allowedClients: string[]) => {
	return (req: Request, res: Response, next: NextFunction) => {
		const clientId = getClientId(req);

		if (!clientId || !allowedClients.includes(clientId)) {
			return res.status(403).json({
				success: false,
				message: 'Client not authorized for this resource'
			});
		}

		console.log('✅ Client access validated:', clientId);
		next();
	};
};

// ===== CONTROLLER EXAMPLES =====

/**
 * Example controller that uses user information from request
 */
export const getUserProfileController = (req: Request, res: Response) => {
	// Analyze user from request
	const userAnalysis = analyzeUserFromRequest(req);

	if (!userAnalysis.isValid) {
		return res.status(401).json({
			success: false,
			message: 'User not authenticated'
		});
	}

	// Log detailed user analysis
	logUserFromRequest(req, 'Profile Request');

	// Use the user information
	const response = {
		success: true,
		data: {
			userId: userAnalysis.userId,
			email: userAnalysis.email,
			clientId: userAnalysis.clientId,
			clientOrigin: userAnalysis.clientOrigin,
			isActive: userAnalysis.isActive,
			hasPermissions: userAnalysis.hasPermissions,
			permissions: userAnalysis.permissions
		}
	};

	res.json(response);
};

/**
 * Example controller that processes data for a specific user
 */
export const processUserDataController = (req: Request, res: Response) => {
	// Get user ID from request (convenience function)
	const userId = getUserId(req);
	const userEmail = getUserEmail(req);

	if (!userId) {
		return res.status(401).json({
			success: false,
			message: 'User ID not found in request'
		});
	}

	console.log(`📊 Processing data for user: ${userEmail} (${userId})`);

	// Your business logic here using userId
	// const userData = await getUserData(userId);

	res.json({
		success: true,
		message: `Data processed for user ${userEmail}`,
		userId
	});
};

/**
 * Example controller that handles admin operations
 */
export const adminOperationController = (req: Request, res: Response) => {
	// Check admin permission
	if (!userHasPermission(req, 'admin')) {
		return res.status(403).json({
			success: false,
			message: 'Admin permissions required'
		});
	}

	const adminUser = extractUserFromRequest(req);
	console.log(`👑 Admin operation performed by: ${adminUser.email}`);

	// Your admin logic here

	res.json({
		success: true,
		message: 'Admin operation completed',
		performedBy: adminUser.email
	});
};

// ===== DEBUGGING EXAMPLES =====

/**
 * Example function that compares raw JWT with parsed user
 */
export const debugTokenComparison = (req: Request, rawToken: string) => {
	console.log('\n🔬 === TOKEN DEBUGGING SESSION ===');

	// Analyze raw JWT token
	console.log('📝 Raw JWT Analysis:');
	logTokenAnalysis(rawToken, 'Raw JWT');

	// Analyze parsed user from request
	console.log('👤 Parsed User Analysis:');
	logUserFromRequest(req, 'Request User');

	// Compare the data
	const tokenAnalysis = analyzeToken(rawToken);
	const userAnalysis = analyzeUserFromRequest(req);

	console.log('🔄 Comparison:');
	console.log('   User ID Match:', tokenAnalysis.userId === userAnalysis.userId ? '✅' : '❌');
	console.log('   Email Match:', tokenAnalysis.email === userAnalysis.email ? '✅' : '❌');
	console.log(
		'   Client ID Match:',
		tokenAnalysis.clientId === userAnalysis.clientId ? '✅' : '❌'
	);

	console.log('=== END DEBUGGING SESSION ===\n');
};

// ===== UTILITY EXAMPLES =====

/**
 * Example utility to create audit log entry
 */
export const createAuditLog = (req: Request, action: string, resource: string) => {
	const userInfo = extractUserFromRequest(req);

	if (!userInfo.isValid) {
		console.warn('⚠️ Audit log: No user information available');
		return;
	}

	const auditEntry = {
		timestamp: new Date().toISOString(),
		userId: userInfo.userId,
		userEmail: userInfo.email,
		clientId: userInfo.clientId,
		action,
		resource,
		ip: req.ip,
		userAgent: req.get('User-Agent')
	};

	console.log('📋 Audit Log:', auditEntry);

	// Save to database or logging service
	// await saveAuditLog(auditEntry);
};

/**
 * Example utility to check if user can access resource
 */
export const canUserAccessResource = (req: Request, resourceOwnerId: string): boolean => {
	const userId = getUserId(req);
	const isAdmin = userHasPermission(req, 'admin');

	// User can access if they own the resource or are admin
	return userId === resourceOwnerId || isAdmin;
};

/**
 * Example utility to get user context for business logic
 */
export const getUserContext = (req: Request) => {
	const userAnalysis = analyzeUserFromRequest(req);

	if (!userAnalysis.isValid) {
		throw new Error('User context not available');
	}

	return {
		userId: userAnalysis.userId!,
		email: userAnalysis.email!,
		clientId: userAnalysis.clientId!,
		clientOrigin: userAnalysis.clientOrigin!,
		isActive: userAnalysis.isActive!,
		permissions: userAnalysis.permissions || [],
		isAdmin: userHasPermission(req, 'admin')
	};
};