import { EnumClientO<PERSON><PERSON>, EnumClientPath } from 'excelytics.shared-internals';
import type { Request } from 'express';

export interface AuthenticatedRequest extends Request {}

// This is what you'd typically attach to request.user
export interface UserPayload {
	userId: string;
	email: string;
	roles?: string[] | undefined;
	clientId?: string;
	clientOrigin?: EnumClientOrigin;
	clientPath?: EnumClientPath;
	isActive?: boolean;
}
