import { getModelForClass, modelOptions, index, prop } from '@typegoose/typegoose';

@index({ token: 1 })
@index({ userId: 1 })
@modelOptions({
	schemaOptions: {
		collection: 'RefreshTokens',
		timestamps: true,
	},
})
export class RefreshTokens {
	@prop({ required: true, unique: true })
	public tokenHash!: string; // Stores a hash of the token, not the token itself

	@prop({ required: true })
	public userId!: string; // userId from Identity model

	@prop({ required: true })
	public clientId!: string; // clientId from Finance Users model

	@prop({ required: true })
	public issuedAt!: Date;

	@prop({ required: true })
	public expiresAt!: Date;

	@prop({ default: false })
	public isRevoked!: boolean;

	@prop()
	public lastUsed?: Date;

	@prop()
	public revokedReason?: string;
}

const RefreshTokenModel = getModelForClass(RefreshTokens);
export default RefreshTokenModel;
