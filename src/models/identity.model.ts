import { getModelForClass, modelOptions, prop, index } from '@typegoose/typegoose';
import { EnumClientOrigin, UserRoles } from 'excelytics.shared-internals';

@modelOptions({
	schemaOptions: {
		collection: 'Identity',
		timestamps: true, // Adds createdAt and updatedAt fields
		toJSON: {
			// Ensure Password and _id are not returned in the response
			transform: (doc, ret, _) => {
				ret.id = ret._id;
				delete ret._id;
				delete ret.__v;
				delete ret.password;
			}
		}
	}
})
export class Identity {
	// Add this property to match what gets added by the toJSON transform
	public id!: string;

    @prop({ required: true, unique: true })
	public clientId!: string; // user Id from Finance instance (users table)

    @prop({ enum: EnumClientOrigin, required: true, type: Number })
    public clientOrigin!: EnumClientOrigin;

    // Note that unique creates an index on email
    @prop({ required: true, unique: true })
    public email!: string;

    // Hashed password
    @prop({ required: true })
    public password!: string;

	@prop({ default: true })
    public isActive!: boolean;

	// backward compatible with static enum or string array
	@prop({ type: [String], default: [UserRoles.USER] })
	public roles!: string[];

	@prop()
	public lastLogin?: Date;
}

const IdentityModel = getModelForClass(Identity);
export default IdentityModel;