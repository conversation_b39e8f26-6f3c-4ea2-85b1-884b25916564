import { getModelForClass, modelOptions, index, prop } from '@typegoose/typegoose';
import { EnumClientOrigin, UserRoles } from 'excelytics.shared-internals';

// Indexes for performance (unique indexes are handled by @prop unique: true)
@index({ isActive: 1 })
@index({ roles: 1 })
@index({ lastLogin: 1 })
@index({ emailVerified: 1 })
@index({ failedLoginAttempts: 1 })
@modelOptions({
	schemaOptions: {
		collection: 'Identity',
		timestamps: true, // Adds createdAt and updatedAt fields
		toJSON: {
			// Ensure Password and _id are not returned in the response
			transform: (doc, ret, _) => {
				ret.id = ret._id;
				delete ret._id;
				delete ret.__v;
				delete ret.password;
			},
		},
	},
})
export class Identity {
	// Add this property to match what gets added by the toJSON transform
	public id!: string;

	@prop({ required: true, unique: true })
	public clientId!: string; // user Id from Finance instance (users table)

	@prop({ enum: EnumClientOrigin, required: true, type: Number })
	public clientOrigin!: EnumClientOrigin;

	// Note that unique creates an index on email
	@prop({ required: true, unique: true })
	public email!: string;

	// Hashed password
	@prop({ required: true })
	public password!: string;

	@prop({ default: true })
	public isActive!: boolean;

	// backward compatible with static enum or string array
	@prop({ type: [String], default: [UserRoles.USER] })
	public roles!: string[];

	@prop()
	public lastLogin?: Date;

	// --- Metadata for cross-service coordination --- (optional for cross-compatibility)
	@prop()
	public profileCompletedAt?: Date; // When user completed profile in Finance service

	@prop({ default: false })
	public emailVerified!: boolean; // Email verification status

	@prop()
	public emailVerifiedAt?: Date; // When email was verified

	// Security tracking
	@prop({ default: 0 })
	public failedLoginAttempts?: number; // Track failed login attempts

	@prop()
	public lockedUntil?: Date; // Account lockout timestamp

	@prop()
	public passwordChangedAt?: Date; // Last password change
}

const IdentityModel = getModelForClass(Identity);
export default IdentityModel;
