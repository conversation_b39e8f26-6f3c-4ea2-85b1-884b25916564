# Prettier Configuration Documentation

## Overview

This document explains the improved Prettier configuration for the
Introspection.Finance.Identity project, designed to work harmoniously with
ESLint and provide consistent, readable code formatting.

## 🔧 Configuration Changes Made

### Key Improvements

1. **Fixed the "extra newline" issue** - Added `.editorconfig` with
   `insert_final_newline = false`
2. **Increased line width** from 100 to 120 characters for better readability
3. **Added ES5 trailing commas** for better git diffs and compatibility
4. **Added file-specific overrides** for different file types
5. **Improved `.prettierignore`** to be less restrictive
6. **Added comprehensive `.editorconfig`** for consistent editor behavior

### Main Configuration (`.prettierrc`)

```json
{
  "$schema": "https://json.schemastore.org/prettierrc",
  "semi": true,
  "tabWidth": 4,
  "useTabs": true,
  "printWidth": 120,
  "singleQuote": true,
  "quoteProps": "as-needed",
  "trailingComma": "es5",
  "bracketSpacing": true,
  "bracketSameLine": false,
  "arrowParens": "avoid",
  "endOfLine": "lf",
  "proseWrap": "preserve",
  "htmlWhitespaceSensitivity": "css",
  "embeddedLanguageFormatting": "auto",
  "singleAttributePerLine": false
}
```

## 📋 Configuration Options Explained

### Core Formatting

| Option        | Value  | Reason                                    | ESLint Alignment              |
| ------------- | ------ | ----------------------------------------- | ----------------------------- |
| `semi`        | `true` | Matches ESLint preference for semicolons  | ✅ Compatible                 |
| `tabWidth`    | `4`    | Matches ESLint `indent` rule              | ✅ Matches `['error', 'tab']` |
| `useTabs`     | `true` | Consistent with ESLint tab preference     | ✅ Matches ESLint config      |
| `printWidth`  | `120`  | Increased from 100 for better readability | ✅ No ESLint conflict         |
| `singleQuote` | `true` | Consistent string quoting                 | ✅ No ESLint conflict         |

### Advanced Options

| Option            | Value     | Purpose                             |
| ----------------- | --------- | ----------------------------------- |
| `trailingComma`   | `"es5"`   | Better git diffs, ES5 compatibility |
| `bracketSpacing`  | `true`    | `{ foo }` instead of `{foo}`        |
| `bracketSameLine` | `false`   | JSX closing bracket on new line     |
| `arrowParens`     | `"avoid"` | `x => x` instead of `(x) => x`      |
| `endOfLine`       | `"lf"`    | Unix line endings for consistency   |

## 🎯 File-Specific Overrides

### JSON Files

```json
{
  "files": ["*.json", "*.jsonc"],
  "options": {
    "tabWidth": 2,
    "useTabs": false
  }
}
```

**Reason:** JSON files are typically formatted with 2 spaces

### Markdown Files

```json
{
  "files": ["*.md", "*.mdx"],
  "options": {
    "tabWidth": 2,
    "useTabs": false,
    "printWidth": 80,
    "proseWrap": "always"
  }
}
```

**Reason:** Markdown benefits from narrower width and space indentation

### YAML Files

```json
{
  "files": ["*.yml", "*.yaml"],
  "options": {
    "tabWidth": 2,
    "useTabs": false
  }
}
```

**Reason:** YAML requires consistent spacing, typically 2 spaces

### Package.json

```json
{
  "files": ["package.json"],
  "options": {
    "tabWidth": 4,
    "useTabs": false
  }
}
```

**Reason:** Package.json benefits from 4-space indentation for readability

## 🚫 Prettier Ignore Configuration

### What's Ignored

- **Build outputs**: `dist/`, `build/`, `*.tsbuildinfo`
- **Dependencies**: `node_modules/`, `bun.lockb`
- **Logs**: `logs/`, `*.log`
- **Cache files**: `.eslintcache`, `.cache/`
- **Environment files**: `.env*`
- **Generated files**: `types/`, `shared/`
- **IDE/OS files**: `.vscode/`, `.DS_Store`

### What's NOT Ignored (Fixed)

- ✅ **TypeScript files** (`.ts`, `.tsx`)
- ✅ **JavaScript files** (`.js`, `.mjs`) - Now properly formatted
- ✅ **JSON files** - With specific formatting rules
- ✅ **Source code** - All source files are now formatted

## 🔄 ESLint Integration

### Compatible Rules

| ESLint Rule                   | Prettier Setting             | Status         |
| ----------------------------- | ---------------------------- | -------------- |
| `indent: ['error', 'tab']`    | `useTabs: true, tabWidth: 4` | ✅ Compatible  |
| `prefer-const`                | N/A                          | ✅ No conflict |
| `no-var`                      | N/A                          | ✅ No conflict |
| `import/newline-after-import` | N/A                          | ✅ No conflict |

### Disabled ESLint Rules

These ESLint rules should be disabled to prevent conflicts:

```javascript
{
  "rules": {
    "indent": "off", // Handled by Prettier
    "quotes": "off", // Handled by Prettier
    "semi": "off", // Handled by Prettier
    "comma-dangle": "off" // Handled by Prettier
  }
}
```

## 🚀 Usage Commands

### Format All Files

```bash
bun run format:fix
```

### Check Formatting

```bash
bun run check:format
```

### Format Specific File

```bash
npx prettier --write src/server.ts
```

### Check Specific File

```bash
npx prettier --check src/server.ts
```

## 🐛 Common Issues Fixed

### ❌ Before (Issues)

1. **Extra newlines** at end of files
2. **Inconsistent line width** (100 was too narrow)
3. **No trailing commas** (poor git diffs)
4. **Over-restrictive ignore** (ignored all .js/.json files)

### ✅ After (Fixed)

1. **No extra newlines** (`insertFinalNewline: false`)
2. **Better line width** (120 characters)
3. **ES5 trailing commas** (better git diffs)
4. **Proper ignore rules** (only ignores build/cache files)

## 💡 Best Practices

### Development Workflow

1. **Set up editor integration** for format-on-save
2. **Run `check:format`** before commits
3. **Use `format:fix`** to fix all formatting issues
4. **Combine with ESLint** for complete code quality

### VS Code Integration

Add to your VS Code settings:

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
```

## 📊 Before vs After Comparison

### Line Width Example

```typescript
// Before (100 chars) - Forces awkward breaks
const response = await request.post('/api/v1/auth/register').send(invalidData);

// After (120 chars) - More natural formatting
const response = await request
  .post('/api/v1/auth/register')
  .send(invalidData)
  .expect(HttpStatus.BAD_REQUEST);
```

### Trailing Commas Example

```typescript
// Before (no trailing commas)
const config = {
  host: 'localhost',
  port: 3000,
  ssl: false,
};

// After (ES5 trailing commas)
const config = {
  host: 'localhost',
  port: 3000,
  ssl: false,
};
```

This configuration provides a perfect balance between strict formatting and
developer productivity! 🎉
