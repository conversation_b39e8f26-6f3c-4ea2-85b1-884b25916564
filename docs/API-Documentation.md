# Identity Provider (IdP) API Documentation

## Overview

The Introspection.Finance.Identity service provides comprehensive authentication and authorization capabilities for the Excelytics microservices ecosystem. This document details the API structure, token handling, session management, and security features.

## Table of Contents

- [API Endpoints](#api-endpoints)
- [Authentication Flow](#authentication-flow)
- [Token Structure](#token-structure)
- [Session Management](#session-management)
- [CORS Configuration](#cors-configuration)
- [Security Headers](#security-headers)
- [Error Handling](#error-handling)
- [Rate Limiting](#rate-limiting)

## API Endpoints

### Base URL
```
Development: http://localhost:6002/api/v1
Production: https://auth.excelytics.co.za/api/v1
```

### Authentication Endpoints

#### POST `/auth/register`
Register a new user account.

**Request Headers:**
```http
Content-Type: application/json
X-Request-ID: <optional-request-id>
X-Client-Version: <optional-client-version>
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword@123",
  "clientId": "507f1f77bcf86cd799439011",
  "clientOrigin": 1
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "message": "User registered and logged in successfully",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenPayload": {
      "userId": "507f1f77bcf86cd799439011",
      "email": "<EMAIL>",
      "clientId": "507f1f77bcf86cd799439011",
      "clientOrigin": 1,
      "clientPath": "excelytics.identity",
      "isActive": true,
      "permissions": ["user"],
      "tokenType": "access",
      "issuedAt": **********,
      "iat": **********,
      "exp": **********
    }
  }
}
```

#### POST `/auth/login`
Authenticate existing user.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword@123",
  "clientId": "507f1f77bcf86cd799439011",
  "clientOrigin": 1
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Login successful.",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenPayload": {
      "userId": "507f1f77bcf86cd799439011",
      "email": "<EMAIL>",
      "clientId": "507f1f77bcf86cd799439011",
      "clientOrigin": 1,
      "clientPath": "excelytics.identity",
      "isActive": true,
      "permissions": ["user"],
      "tokenType": "access",
      "issuedAt": **********,
      "iat": **********,
      "exp": **********
    }
  }
}
```

#### POST `/auth/refresh`
Refresh access token using refresh token.

**Request Headers:**
```http
Content-Type: application/json
Authorization: Bearer <refresh-token>
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenPayload": {
      "userId": "507f1f77bcf86cd799439011",
      "email": "<EMAIL>",
      "clientId": "507f1f77bcf86cd799439011",
      "clientOrigin": 1,
      "clientPath": "excelytics.identity",
      "isActive": true,
      "permissions": ["user"],
      "tokenType": "access",
      "issuedAt": **********,
      "iat": **********,
      "exp": **********
    }
  }
}
```

#### GET `/auth/session`
Get or create session information.

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Session retrieved successfully",
  "data": {
    "sessionId": "sess:abc123def456",
    "created": "2025-06-26T22:47:26.000Z",
    "lastAccessed": "2025-06-26T22:47:26.000Z",
    "initialized": true,
    "cookie": {
      "maxAge": 3600000,
      "secure": false,
      "httpOnly": true,
      "sameSite": "lax"
    }
  }
}
```

### Token Validation Endpoints

#### POST `/verify-access-token`
Validate access token (OAuth2 Introspection).

**Request Body:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response (200 OK) - Valid Token:**
```json
{
  "success": true,
  "message": "Token validation completed",
  "data": {
    "active": true,
    "sub": "507f1f77bcf86cd799439011",
    "email": "<EMAIL>",
    "client_id": "507f1f77bcf86cd799439011",
    "token_type": "access",
    "exp": **********,
    "iat": **********,
    "iss": "excelytics.identity"
  }
}
```

**Response (200 OK) - Invalid Token:**
```json
{
  "success": true,
  "message": "Token validation completed",
  "data": {
    "active": false
  }
}
```

#### POST `/introspect`
OAuth2 standard introspection endpoint.

**Request Headers:**
```http
Content-Type: application/x-www-form-urlencoded
Authorization: Basic <client-credentials>
```

**Request Body:**
```
token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Health Check Endpoints

#### GET `/health`
Basic health check.

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Service is healthy",
  "data": {
    "status": "healthy",
    "timestamp": "2025-06-26T22:47:26.000Z",
    "service": "excelytics.identity",
    "version": "1.0.0"
  }
}
```

#### GET `/health/all`
Comprehensive health check including database connectivity.

**Response (200 OK):**
```json
{
  "success": true,
  "message": "All systems operational",
  "data": {
    "status": "healthy",
    "timestamp": "2025-06-26T22:47:26.000Z",
    "service": "excelytics.identity",
    "version": "1.0.0",
    "database": {
      "status": "connected",
      "responseTime": "5ms"
    },
    "memory": {
      "used": "45MB",
      "total": "512MB"
    }
  }
}
```

## Authentication Flow

### Registration and Login Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant IdP as Identity Provider
    participant DB as MongoDB
    participant JWT as JWT Service

    Note over C,JWT: User Registration Flow
    C->>IdP: POST /auth/register
    IdP->>DB: Check if user exists
    DB-->>IdP: User not found
    IdP->>DB: Create new user
    DB-->>IdP: User created
    IdP->>JWT: Generate tokens
    JWT-->>IdP: Access + Refresh tokens
    IdP-->>C: 201 Created + tokens

    Note over C,JWT: User Login Flow
    C->>IdP: POST /auth/login
    IdP->>DB: Validate credentials
    DB-->>IdP: Credentials valid
    IdP->>JWT: Generate tokens
    JWT-->>IdP: Access + Refresh tokens
    IdP-->>C: 200 OK + tokens

    Note over C,JWT: Token Refresh Flow
    C->>IdP: POST /auth/refresh (with refresh token)
    IdP->>JWT: Validate refresh token
    JWT-->>IdP: Token valid
    IdP->>JWT: Generate new tokens
    JWT-->>IdP: New Access + Refresh tokens
    IdP-->>C: 200 OK + new tokens
```

### Token Validation Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as API Service
    participant IdP as Identity Provider
    participant JWT as JWT Service

    Note over Client,JWT: API Request with Token
    Client->>API: API Request + Bearer Token
    API->>IdP: POST /verify-access-token
    IdP->>JWT: Validate token
    JWT-->>IdP: Token validation result
    
    alt Token Valid
        IdP-->>API: 200 OK + user data
        API->>API: Process request
        API-->>Client: API Response
    else Token Invalid
        IdP-->>API: 200 OK + active: false
        API-->>Client: 401 Unauthorized
    end
```

## Token Structure

### Access Token Payload
```json
{
  "userId": "507f1f77bcf86cd799439011",
  "email": "<EMAIL>",
  "clientId": "507f1f77bcf86cd799439011",
  "clientOrigin": 1,
  "clientPath": "excelytics.identity",
  "isActive": true,
  "permissions": ["user"],
  "tokenType": "access",
  "issuedAt": **********,
  "iat": **********,
  "exp": **********
}
```

### Refresh Token Payload
```json
{
  "userId": "507f1f77bcf86cd799439011",
  "email": "<EMAIL>",
  "clientId": "507f1f77bcf86cd799439011",
  "clientOrigin": 1,
  "clientPath": "excelytics.identity",
  "isActive": true,
  "tokenType": "refresh",
  "issuedAt": **********,
  "iat": **********,
  "exp": 1641600000
}
```

### Token Decoding Example (JavaScript)
```javascript
// Decode JWT token (client-side)
function decodeToken(token) {
  const payload = JSON.parse(atob(token.split('.')[1]));
  return payload;
}

// Extract user information
const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...";
const userInfo = decodeToken(token);

console.log('User ID:', userInfo.userId);
console.log('Email:', userInfo.email);
console.log('Permissions:', userInfo.permissions);
console.log('Expires:', new Date(userInfo.exp * 1000));
```

### Token Validation (Server-side)
```javascript
// Validate token server-side
async function validateToken(token) {
  const response = await fetch('/api/v1/verify-access-token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ token })
  });
  
  const result = await response.json();
  return result.data.active;
}
```

## Session Management

### Session Configuration
- **Session Store**: MongoDB (production) / Memory (development)
- **Session Name**: `excelytics.idp.sid`
- **Max Age**: 1 hour (configurable)
- **Security**: HttpOnly, SameSite=Lax (dev) / Strict (prod)
- **Secure**: HTTPS only in production

### Session Cookie Attributes
```http
Set-Cookie: excelytics.idp.sid=s%3A...;
  Path=/;
  HttpOnly;
  SameSite=Lax;
  Max-Age=3600
```

### Session Data Structure
```json
{
  "sessionId": "sess:abc123def456",
  "data": {
    "created": "2025-06-26T22:47:26.000Z",
    "lastAccessed": "2025-06-26T22:47:26.000Z",
    "initialized": true,
    "userId": "507f1f77bcf86cd799439011",
    "email": "<EMAIL>"
  }
}
```

## CORS Configuration

### Allowed Origins
```javascript
[
  // Local development
  'http://localhost:6002',  // Identity service
  'http://localhost:6003',  // Finance API
  'http://localhost:4200',  // Frontend application
  'http://localhost:6001',  // Calc engine

  // Production domains
  'https://auth.excelytics.co.za',
  'https://app.excelytics.co.za',
  'https://excelytics.co.za',
  'https://api.excelytics.co.za'
]
```

### Allowed Headers
```javascript
[
  // Standard HTTP headers
  'Content-Type', 'Authorization', 'Accept',
  'Accept-Language', 'Accept-Encoding', 'Origin',
  'Referer', 'User-Agent',

  // Custom service headers
  'X-Service-Name', 'X-Request-ID', 'X-Correlation-ID',
  'X-Client-Version', 'X-Forwarded-For', 'X-Real-IP',

  // Caching headers
  'Cache-Control', 'Pragma', 'If-None-Match',
  'If-Modified-Since'
]
```

### Exposed Headers
```javascript
[
  // Request tracking
  'X-Request-ID', 'X-Correlation-ID',

  // Rate limiting
  'X-RateLimit-Limit', 'X-RateLimit-Reset',
  'X-RateLimit-Remaining', 'Retry-After',

  // Standard HTTP response headers
  'Location', 'Content-Length', 'Content-Range',
  'ETag', 'Last-Modified',

  // Pagination
  'X-Total-Count', 'X-Page-Count', 'Link',

  // Service information
  'X-Service', 'X-API-Version', 'X-Environment'
]
```

## Security Headers

### Response Headers
```http
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
X-Permitted-Cross-Domain-Policies: none
X-Service: excelytics.identity
X-API-Version: 1.0.0
X-Environment: development
X-Request-ID: req_abc123def456
```

### Rate Limiting Headers
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
Retry-After: 60
```

## Error Handling

### Error Response Structure
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Email is required",
    "details": {
      "field": "email",
      "value": null,
      "constraint": "required"
    }
  },
  "timestamp": "2025-06-26T22:47:26.000Z",
  "requestId": "req_abc123def456"
}
```

### Common Error Codes
- `VALIDATION_ERROR` (400): Input validation failed
- `UNAUTHORIZED` (401): Authentication required
- `FORBIDDEN` (403): Insufficient permissions
- `NOT_FOUND` (404): Resource not found
- `CONFLICT` (409): Resource already exists
- `RATE_LIMITED` (429): Too many requests
- `INTERNAL_SERVER_ERROR` (500): Server error

## Rate Limiting

### Rate Limit Configuration
- **Window**: 15 minutes
- **Max Requests**: 100 per window per IP
- **Skip Successful Requests**: false
- **Headers**: Exposed in response

### Rate Limit Response
```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMITED",
    "message": "Too many requests, please try again later"
  },
  "retryAfter": 60
}
```

## Microservices Integration

### Service-to-Service Authentication
```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web App]
        MOBILE[Mobile App]
        ADMIN[Admin Panel]
    end

    subgraph "API Gateway"
        NGINX[Nginx Gateway]
    end

    subgraph "Microservices"
        IDP[Identity Provider<br/>Port 6002]
        API[Finance API<br/>Port 6003]
        CALC[Calc Engine<br/>Port 6001]
        FILE[File Service<br/>Port 6004]
    end

    subgraph "Data Layer"
        MONGO[(MongoDB)]
        REDIS[(Redis)]
    end

    WEB --> NGINX
    MOBILE --> NGINX
    ADMIN --> NGINX

    NGINX --> IDP
    NGINX --> API
    NGINX --> CALC
    NGINX --> FILE

    API --> IDP
    CALC --> IDP
    FILE --> IDP

    IDP --> MONGO
    API --> MONGO
    CALC --> REDIS
    FILE --> MONGO
```

### Token Flow in Microservices
```mermaid
sequenceDiagram
    participant Client as Client App
    participant Gateway as API Gateway
    participant API as Finance API
    participant IdP as Identity Provider
    participant DB as Database

    Client->>Gateway: Request + JWT Token
    Gateway->>API: Forward Request + Token
    API->>IdP: Validate Token
    IdP->>IdP: Verify JWT Signature

    alt Token Valid
        IdP-->>API: User Data + Permissions
        API->>DB: Query with User Context
        DB-->>API: Data Response
        API-->>Gateway: API Response
        Gateway-->>Client: Final Response
    else Token Invalid
        IdP-->>API: Invalid Token
        API-->>Gateway: 401 Unauthorized
        Gateway-->>Client: 401 Unauthorized
    end
```

## API Usage Examples

### Complete Authentication Flow
```javascript
// 1. Register new user
const registerResponse = await fetch('/api/v1/auth/register', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Client-Version': '1.0.0'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'SecurePassword@123',
    clientId: '507f1f77bcf86cd799439011',
    clientOrigin: 1
  })
});

const { data: authData } = await registerResponse.json();
const { token, refreshToken, tokenPayload } = authData;

// 2. Store tokens securely
localStorage.setItem('accessToken', token);
localStorage.setItem('refreshToken', refreshToken);

// 3. Use token for API calls
const apiResponse = await fetch('/api/v1/finance/data', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'X-Request-ID': 'req_' + Date.now()
  }
});

// 4. Handle token expiration
if (apiResponse.status === 401) {
  // Refresh token
  const refreshResponse = await fetch('/api/v1/auth/refresh', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${refreshToken}`
    }
  });

  if (refreshResponse.ok) {
    const { data: newAuthData } = await refreshResponse.json();
    localStorage.setItem('accessToken', newAuthData.token);
    localStorage.setItem('refreshToken', newAuthData.refreshToken);
  } else {
    // Redirect to login
    window.location.href = '/login';
  }
}
```

---

## Summary

The Introspection.Finance.Identity service provides:

- **Secure Authentication**: JWT-based with refresh tokens
- **Session Management**: MongoDB-backed sessions with security
- **OAuth2 Compliance**: Standard introspection endpoints
- **Comprehensive CORS**: Support for microservices architecture
- **Rate Limiting**: Protection against abuse
- **Security Headers**: Defense against common attacks
- **Detailed Logging**: Request tracking and debugging
- **Health Monitoring**: Service status and diagnostics

For additional information, see:
- [CORS Configuration Documentation](./CORS-Configuration.md)
- [Security Best Practices](./Security-Best-Practices.md)
- [Deployment Guide](./Deployment-Guide.md)
```
