# Identity Model & Security Architecture

## Overview

The Introspection.Finance.Identity service implements a comprehensive Role-Based Access Control (RBAC) system that provides secure authentication and authorization for the entire Excelytics microservices ecosystem. This document details the Identity model, security architecture, role-to-permission conversion, and access control mechanisms.

## Table of Contents

- [Identity Model](#identity-model)
- [Security Architecture](#security-architecture)
- [Role-Based Access Control](#role-based-access-control)
- [Permission System](#permission-system)
- [Token Generation & Validation](#token-generation--validation)
- [Authentication Middleware](#authentication-middleware)
- [Access Control Flow](#access-control-flow)
- [Security Best Practices](#security-best-practices)

## Identity Model

### Database Schema

The Identity model represents user accounts in the system with the following structure:

```typescript
@modelOptions({
	schemaOptions: {
		collection: 'Identity',
		timestamps: true, // Adds createdAt and updatedAt fields
		toJSON: {
			// Security: Password and _id are never returned in responses
			transform: (doc, ret, _) => {
				ret.id = ret._id;
				delete ret._id;
				delete ret.__v;
				delete ret.password; // Critical: Never expose passwords
			}
		}
	}
})
export class Identity {
	public id!: string; // Auto-generated MongoDB ObjectId

	@prop({ required: true, unique: true })
	public clientId!: string; // User ID from Finance service

	@prop({ enum: EnumClientOrigin, required: true, type: Number })
	public clientOrigin!: EnumClientOrigin; // Service origin (Excelytics = 1)

	@prop({ required: true, unique: true })
	public email!: string; // Unique email address

	@prop({ required: true })
	public password!: string; // bcrypt hashed password

	@prop({ default: true })
	public isActive!: boolean; // Account status flag

	@prop({ type: [String], default: [UserRoles.USER] })
	public roles!: string[]; // User roles array

	@prop()
	public lastLogin?: Date; // Last successful login timestamp
}
```

### Field Descriptions

| Field          | Type               | Description      | Security Notes                                   |
| -------------- | ------------------ | ---------------- | ------------------------------------------------ |
| `id`           | `string`           | MongoDB ObjectId | Auto-generated, used for internal references     |
| `clientId`     | `string`           | External user ID | Links to Finance service user records            |
| `clientOrigin` | `EnumClientOrigin` | Service origin   | Identifies which service the user belongs to     |
| `email`        | `string`           | User email       | Unique identifier, used for login                |
| `password`     | `string`           | Hashed password  | bcrypt hashed, never returned in responses       |
| `isActive`     | `boolean`          | Account status   | Controls access, checked on every request        |
| `roles`        | `string[]`         | User roles       | Converted to permissions during token generation |
| `lastLogin`    | `Date?`            | Last login time  | Audit trail for security monitoring              |

### Security Features

1. **Password Security**: All passwords are hashed using bcrypt before storage
2. **Data Sanitization**: Passwords and internal IDs are automatically removed from JSON responses
3. **Unique Constraints**: Email addresses must be unique across the system
4. **Account Status**: `isActive` flag allows for account suspension without deletion
5. **Audit Trail**: Timestamps track account creation, updates, and last login

## Security Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Applications"
        WEB[Web Client]
        MOBILE[Mobile App]
        API_CLIENT[API Client]
    end

    subgraph "Identity Provider (IdP)"
        AUTH[Authentication Service]
        JWT[JWT Service]
        IDENTITY[Identity Model]
        MIDDLEWARE[Auth Middleware]
    end

    subgraph "Microservices"
        FINANCE[Finance Service]
        CALC[Calc Service]
        CLIENT[Client Service]
    end

    subgraph "Database"
        MONGO[(MongoDB)]
        REDIS[(Redis Sessions)]
    end

    WEB --> AUTH
    MOBILE --> AUTH
    API_CLIENT --> AUTH

    AUTH --> JWT
    AUTH --> IDENTITY
    IDENTITY --> MONGO

    JWT --> FINANCE
    JWT --> CALC
    JWT --> CLIENT

    MIDDLEWARE --> REDIS

    classDef client fill:#e3f2fd
    classDef idp fill:#e8f5e8
    classDef service fill:#fff3e0
    classDef database fill:#fce4ec

    class WEB,MOBILE,API_CLIENT client
    class AUTH,JWT,IDENTITY,MIDDLEWARE idp
    class FINANCE,CALC,CLIENT service
    class MONGO,REDIS database
```

### Core Security Principles

1. **Centralized Authentication**: Single IdP for all microservices
2. **Stateless Tokens**: JWT tokens eliminate server-side session storage
3. **Role-Based Access**: Users assigned roles, roles mapped to permissions
4. **Token Validation**: Every request validates token integrity and user status
5. **Principle of Least Privilege**: Users get minimum permissions needed
6. **Defense in Depth**: Multiple security layers (auth, validation, middleware)

## Role-Based Access Control

### Available Roles

The system defines four primary user roles:

```typescript
export const UserRoles = {
	ADMIN: 'admin', // Full system access
	USER: 'user', // Standard user access
	GUEST: 'guest', // Limited access
	NONE: 'none' // No access (disabled accounts)
} as const;
```

### Role Hierarchy

```mermaid
graph TD
    ADMIN[Admin Role<br/>Full System Access]
    USER[User Role<br/>Standard Access]
    GUEST[Guest Role<br/>Limited Access]
    NONE[None Role<br/>No Access]

    ADMIN --> USER
    USER --> GUEST
    GUEST --> NONE

    classDef admin fill:#ffebee
    classDef user fill:#e8f5e8
    classDef guest fill:#fff3e0
    classDef none fill:#f5f5f5

    class ADMIN admin
    class USER user
    class GUEST guest
    class NONE none
```

### Role Assignment

- **Default Role**: New users automatically receive `USER` role
- **Role Storage**: Roles stored as string array in `Identity.roles` field
- **Multiple Roles**: Users can have multiple roles (e.g., `['user', 'admin']`)
- **Role Inheritance**: Higher roles inherit permissions from lower roles

## Permission System

### Permission Structure

Permissions follow a structured format: `{scope}:{action}:{resource}`

```typescript
// Examples of permission strings:
'read:api'; // Global API access
'self:read:files'; // Read own files
'identity:admin:system'; // Admin access to identity service
'finance:manage:financial_data'; // Manage financial data
```

### Permission Components

1. **Scope** (Optional): Service or context (`identity`, `finance`, `calc`, `client`, `self`, `global`)
2. **Action**: What can be done (`read`, `create`, `update`, `delete`, `manage`, `admin`)
3. **Resource**: What it applies to (`users`, `files`, `system`, `api`, `reports`)

### Role-to-Permission Mapping

The system uses `PermissionGroups` to map roles to specific permissions:

```typescript
export const PermissionGroups = {
	SUPER_ADMIN: [
		EnumPermissions.READ_LOGS,
		EnumPermissions.CALC_ADMIN,
		EnumPermissions.ADMIN_SYSTEM,
		EnumPermissions.MANAGE_USERS,
		EnumPermissions.CLIENT_ADMIN,
		EnumPermissions.FINANCE_ADMIN,
		EnumPermissions.IDENTITY_ADMIN,
		EnumPermissions.MANAGE_SESSIONS,
		EnumPermissions.MANAGE_SETTINGS
	],

	USER: [
		// Self operations
		EnumPermissions.READ_SELF_FILES,
		EnumPermissions.READ_SELF_PROFILE,
		EnumPermissions.UPLOAD_SELF_FILES,
		EnumPermissions.UPDATE_SELF_PROFILE,
		EnumPermissions.DOWNLOAD_SELF_FILES,
		EnumPermissions.READ_SELF_CALCULATIONS,
		EnumPermissions.READ_SELF_FINANCIAL_DATA,
		EnumPermissions.CREATE_SELF_CALCULATIONS,
		EnumPermissions.CREATE_SELF_FINANCIAL_DATA,

		// Basic read operations
		EnumPermissions.READ_CHARTS,
		EnumPermissions.READ_TEMPLATES,

		// Basic API access
		EnumPermissions.ACCESS_API,
		EnumPermissions.ACCESS_CLIENT_API
	],

	GUEST: [EnumPermissions.ACCESS_API, EnumPermissions.LOGIN_SYSTEM, EnumPermissions.READ_TEMPLATES]
} as const;
```

### Role-to-Permission Converter

The `AuthenticationService` includes a critical method that converts user roles to permissions:

```typescript
/**
 * Converts user roles to actual permissions using PermissionGroups mapping
 * @param roles - Array of user role strings
 * @returns Array of permission strings
 */
private convertRolesToPermissions(roles: string[]): string[] {
    const permissions = new Set<string>();

    for (const role of roles) {
        switch (role.toLowerCase()) {
            case UserRoles.ADMIN:
                // Admin users get super admin permissions
                PermissionGroups.SUPER_ADMIN.forEach(permission => permissions.add(permission));
                break;
            case UserRoles.USER:
                // Regular users get basic user permissions
                PermissionGroups.USER.forEach(permission => permissions.add(permission));
                break;
            case UserRoles.GUEST:
                // Guest users get minimal permissions
                PermissionGroups.GUEST.forEach(permission => permissions.add(permission));
                break;
            default:
                // Unknown roles get basic user permissions as fallback
                PermissionGroups.USER.forEach(permission => permissions.add(permission));
                break;
        }
    }

    return Array.from(permissions);
}
```

**Key Features:**

- **Deduplication**: Uses `Set` to prevent duplicate permissions
- **Role Inheritance**: Admin users get both admin and user permissions
- **Fallback Security**: Unknown roles default to basic user permissions
- **Multiple Roles**: Handles users with multiple roles correctly

## Token Generation & Validation

### Token Generation Process

```mermaid
sequenceDiagram
    participant User
    participant AuthService
    participant Converter
    participant JWTService
    participant Database

    User->>AuthService: Login Request
    AuthService->>Database: Validate Credentials
    Database->>AuthService: User Data + Roles
    AuthService->>Converter: convertRolesToPermissions(roles)
    Converter->>AuthService: Permission Array
    AuthService->>JWTService: generateAccessToken(payload)
    JWTService->>AuthService: JWT Token
    AuthService->>User: Token + User Data
```

### Token Structure

**Access Token Payload:**

```typescript
{
    userId: string;              // User's database ID
    email: string;               // User's email
    clientId: string;            // External client ID
    clientOrigin: number;        // Service origin
    clientPath: string;          // Service path
    isActive: boolean;           // Account status
    permissions: string[];       // Converted permissions array
    tokenType: "access";         // Token type
    issuedAt: number;           // Unix timestamp
    iat: number;                // JWT issued at
    exp: number;                // JWT expires at
}
```

**Refresh Token Payload:**

```typescript
{
	userId: string; // User's database ID
	email: string; // User's email
	clientId: string; // External client ID
	clientOrigin: number; // Service origin
	clientPath: string; // Service path
	isActive: boolean; // Account status
	// Note: No permissions in refresh tokens
	tokenType: 'refresh'; // Token type
	issuedAt: number; // Unix timestamp
	iat: number; // JWT issued at
	exp: number; // JWT expires at
}
```

### Token Validation

The system validates tokens using Zod schemas:

```typescript
// Token validation ensures:
// 1. All required fields are present
// 2. Permissions are valid EnumPermissions values
// 3. No duplicate permissions exist
// 4. Basic API access is included when permissions are specified
// 5. Token type matches expected value
// 6. Timestamps are valid Unix timestamps
```

## Authentication Middleware

### requireAuth Middleware

The primary authentication middleware validates every protected request:

```typescript
export const requireAuth = async (request: AuthenticatedRequest, response: Response, next: NextFunction): Promise<void> => {
	try {
		// 1. Extract Bearer token from Authorization header
		const authHeader = request.headers.authorization;
		if (!authHeader || !authHeader.startsWith('Bearer ')) {
			throw new UnauthorizedError('Authentication token is required');
		}

		const token = authHeader.split(' ')[1];

		// 2. Verify token signature and decode payload
		const decodedPayload = jwtService.verifyAccessTokenInternal(token);
		if (!decodedPayload) {
			throw new UnauthorizedError('Invalid or expired authentication token');
		}

		// 3. Verify user still exists and is active
		const user = await IdentityModel.findById(decodedPayload.userId).select('isActive').lean();
		if (!user || !user.isActive) {
			throw new UnauthorizedError('User account is inactive or deleted');
		}

		// 4. Attach user data to request object
		request.user = {
			userId: decodedPayload.userId,
			email: decodedPayload.email,
			clientId: decodedPayload.clientId,
			clientOrigin: decodedPayload.clientOrigin,
			clientPath: decodedPayload.clientPath,
			isActive: decodedPayload.isActive,
			tokenType: decodedPayload.tokenType,
			issuedAt: decodedPayload.issuedAt,
			roles: decodedPayload.permissions // Permissions stored as roles
		};

		next();
	} catch (error) {
		next(error);
	}
};
```

### requireRole Middleware Factory

Creates role-specific middleware for authorization:

```typescript
export const requireRole = (requiredRole: string) => {
	return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
		try {
			// User must be authenticated first
			if (!req.user) {
				throw new UnauthorizedError('Authentication required');
			}

			// Check if user has required role/permission
			if (!req.user.roles?.includes(requiredRole)) {
				throw new ForbiddenError('Insufficient permissions');
			}

			next();
		} catch (error) {
			next(error);
		}
	};
};

// Pre-built admin middleware
export const requireAdminRole = requireRole(UserRoles.ADMIN);
```

## Access Control Flow

### Complete Authentication Flow

```mermaid
flowchart TD
    START([User Request]) --> AUTH_HEADER{Authorization Header?}

    AUTH_HEADER -->|Missing| UNAUTH[401 Unauthorized]
    AUTH_HEADER -->|Present| EXTRACT[Extract Bearer Token]

    EXTRACT --> VERIFY{Verify JWT Signature}
    VERIFY -->|Invalid| UNAUTH
    VERIFY -->|Valid| DECODE[Decode Token Payload]

    DECODE --> VALIDATE{Validate with Zod Schema}
    VALIDATE -->|Invalid| UNAUTH
    VALIDATE -->|Valid| CHECK_USER{User Exists & Active?}

    CHECK_USER -->|No| UNAUTH
    CHECK_USER -->|Yes| ATTACH[Attach User to Request]

    ATTACH --> ROLE_CHECK{Role Required?}
    ROLE_CHECK -->|No| SUCCESS[✅ Access Granted]
    ROLE_CHECK -->|Yes| HAS_ROLE{User Has Role?}

    HAS_ROLE -->|No| FORBIDDEN[403 Forbidden]
    HAS_ROLE -->|Yes| SUCCESS

    classDef success fill:#e8f5e8
    classDef error fill:#ffebee
    classDef process fill:#e3f2fd
    classDef decision fill:#fff3e0

    class SUCCESS success
    class UNAUTH,FORBIDDEN error
    class EXTRACT,DECODE,ATTACH process
    class AUTH_HEADER,VERIFY,VALIDATE,CHECK_USER,ROLE_CHECK,HAS_ROLE decision
```

### Route Protection Examples

```typescript
// Public routes (no authentication required)
router.get('/health', healthController.getHealth);
router.post('/auth/login', authController.login);
router.post('/auth/register', authController.register);

// Protected routes (authentication required)
router.use(asyncHandler(requireAuth));
router.get('/profile', userController.getProfile);
router.put('/profile', userController.updateProfile);

// Admin-only routes (authentication + admin role required)
router.use(asyncHandler(requireAdminRole));
router.get('/users', identityController.getAllUsers);
router.delete('/users/:email', identityController.deleteUser);
```

### Permission Checking in Controllers

```typescript
// Example: Check specific permissions in controller logic
export class FileController {
	async uploadFile(req: AuthenticatedRequest, res: Response) {
		const userPermissions = req.user?.roles || [];

		// Check if user can upload files
		if (!PermissionUtils.hasPermission(userPermissions, EnumPermissions.UPLOAD_SELF_FILES)) {
			throw new ForbiddenError('File upload permission required');
		}

		// Proceed with file upload logic...
	}
}
```

## Security Best Practices

### Implementation Guidelines

1. **Token Security**
    - Use strong JWT secrets (minimum 256 bits)
    - Implement token rotation for refresh tokens
    - Set appropriate expiration times (30 minutes for access, 7 days for refresh)
    - Never store tokens in localStorage (use httpOnly cookies for web)

2. **Password Security**
    - Use bcrypt with minimum 10 rounds
    - Enforce strong password policies
    - Implement account lockout after failed attempts
    - Never log or expose passwords in any form

3. **Database Security**
    - Use unique indexes on email and clientId
    - Implement soft deletes instead of hard deletes
    - Regularly audit user accounts and permissions
    - Monitor for suspicious login patterns

4. **API Security**
    - Always validate tokens on every protected request
    - Check user account status (isActive) on each request
    - Implement rate limiting on authentication endpoints
    - Use HTTPS in production environments

5. **Permission Management**
    - Follow principle of least privilege
    - Regularly review and audit permission groups
    - Document permission changes and their impact
    - Test permission changes thoroughly

### Security Monitoring

```typescript
// Example: Security event logging
export class SecurityLogger {
	static logLoginAttempt(email: string, success: boolean, ip: string) {
		console.log({
			event: 'login_attempt',
			email,
			success,
			ip,
			timestamp: new Date().toISOString()
		});
	}

	static logPermissionDenied(userId: string, requiredPermission: string) {
		console.log({
			event: 'permission_denied',
			userId,
			requiredPermission,
			timestamp: new Date().toISOString()
		});
	}
}
```

### Common Security Pitfalls to Avoid

1. **❌ Don't** store permissions directly in the database without role mapping
2. **❌ Don't** trust client-side role/permission data
3. **❌ Don't** skip token validation on "internal" API calls
4. **❌ Don't** use the same secret for access and refresh tokens
5. **❌ Don't** expose sensitive user data in JWT payloads
6. **✅ Do** validate tokens on every protected request
7. **✅ Do** check user account status in real-time
8. **✅ Do** use role-based permissions with proper mapping
9. **✅ Do** implement proper error handling for security events
10. **✅ Do** regularly audit and update permission groups

## Conclusion

The Introspection.Finance.Identity service implements a robust, scalable security architecture that provides:

- **Centralized Authentication**: Single source of truth for user identity
- **Role-Based Access Control**: Flexible permission system based on user roles
- **Stateless Security**: JWT tokens enable horizontal scaling
- **Defense in Depth**: Multiple security layers protect against various threats
- **Audit Trail**: Comprehensive logging for security monitoring
- **Type Safety**: Strong typing ensures consistency across microservices

This architecture supports the microservices ecosystem while maintaining security best practices and providing a foundation for future scalability and feature expansion.
