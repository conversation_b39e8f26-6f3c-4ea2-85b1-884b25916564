# Improved Finance User Model

## Current vs Recommended Structure

### Current Finance User Model Issues

1. **No link to Identity service** - Missing connection to authentication
2. **Duplicate email field** - Could cause sync issues with Identity
3. **Missing audit fields** - No creation/update tracking
4. **Limited indexing** - Performance concerns for queries
5. **No soft delete** - Hard deletes lose audit trail

### Recommended Improved Finance User Model

```typescript
import { getModelForClass, modelOptions, prop, index, Ref } from '@typegoose/typegoose';
import { EnumUserTypes, EnumEditions } from 'your-enums';
import { Organization } from './organization.model';

// Comprehensive indexing strategy
@index({ identityId: 1 }, { unique: true }) // Link to Identity service
@index({ email: 1 }, { unique: true, sparse: true }) // Sparse for soft deletes
@index({ cellNumber: 1 })
@index({ isActive: 1 })
@index({ organizationId: 1, isActive: 1 }) // Compound index for org queries
@index({ userTypeCode: 1, editionCode: 1 }) // Business logic queries
@index({ createdAt: 1 }) // Audit queries
@index({ updatedAt: 1 }) // Recent changes
@modelOptions({
	schemaOptions: {
		collection: 'User',
		timestamps: true, // Adds createdAt and updatedAt
		toJSON: {
			transform: (doc, ret, _) => {
				ret.id = ret._id;
				delete ret._id;
				delete ret.__v;
				// Don't expose internal sync fields in API responses
				delete ret.identityLastSyncAt;
				delete ret.identitySyncStatus;
			}
		}
	}
})
export class User {
	// Auto-generated ID
	public id!: string;

	// === IDENTITY SERVICE INTEGRATION ===
	@prop({ required: true, unique: true })
	public identityId!: string; // Links to Identity.id from IdP service

	// === BUSINESS RELATIONSHIPS ===
	@prop({ ref: () => Organization, required: true })
	public organizationId!: Ref<Organization>; // Renamed for clarity

	@prop({ enum: EnumUserTypes, required: true, type: Number })
	public userTypeCode!: EnumUserTypes;

	@prop({ enum: EnumEditions, required: true, type: Number })
	public editionCode!: EnumEditions;

	// === PERSONAL INFORMATION ===
	@prop({ required: true })
	public firstName!: string;

	@prop({ required: true })
	public lastName!: string;

	@prop()
	public preferredName?: string; // Made optional with proper typing

	@prop({ required: true, unique: true })
	public email!: string; // Synced from Identity service

	@prop({ required: true })
	public cellNumber!: string;

	// === STATUS & CONTROL ===
	@prop({ required: true, default: true })
	public isActive!: boolean;

	@prop({ default: false })
	public isDeleted!: boolean; // Soft delete flag

	@prop()
	public deletedAt?: Date; // Soft delete timestamp

	@prop()
	public deletedBy?: string; // Who deleted (for audit)

	// === PROFILE COMPLETION ===
	@prop({ default: false })
	public profileCompleted!: boolean;

	@prop()
	public profileCompletedAt?: Date;

	// === SYNC WITH IDENTITY SERVICE ===
	@prop()
	public identityLastSyncAt?: Date; // Last sync with Identity service

	@prop({ enum: ['synced', 'pending', 'failed'], default: 'synced' })
	public identitySyncStatus!: string; // Sync status tracking

	// === BUSINESS METADATA ===
	@prop()
	public department?: string;

	@prop()
	public jobTitle?: string;

	@prop()
	public manager?: string; // Could be Ref<User> for manager relationship

	// === PREFERENCES ===
	@prop({ default: 'en' })
	public preferredLanguage!: string;

	@prop({ default: 'UTC' })
	public timezone!: string;

	// === AUDIT FIELDS ===
	@prop()
	public createdBy?: string; // Who created this user

	@prop()
	public updatedBy?: string; // Who last updated

	// Timestamps are added automatically by timestamps: true
	public createdAt!: Date;
	public updatedAt!: Date;

	// === COMPUTED PROPERTIES ===
	public get fullName(): string {
		return this.preferredName || `${this.firstName} ${this.lastName}`;
	}

	public get displayName(): string {
		return this.preferredName || this.firstName;
	}
}

const UserModel = getModelForClass(User);
export default UserModel;
```

## Key Improvements Explained

### 1. **Identity Service Integration**

```typescript
@prop({ required: true, unique: true })
public identityId!: string; // Links to Identity.id from IdP service
```

- **Purpose**: Creates a strong link between Finance User and Identity
- **Benefits**: Enables cross-service queries and data consistency
- **Usage**: `const user = await UserModel.findOne({ identityId: req.user.userId })`

### 2. **Sync Tracking**

```typescript
@prop()
public identityLastSyncAt?: Date;

@prop({ enum: ['synced', 'pending', 'failed'], default: 'synced' })
public identitySyncStatus!: string;
```

- **Purpose**: Track synchronization status with Identity service
- **Benefits**: Detect and resolve sync issues, audit data consistency
- **Usage**: Monitor when user data gets out of sync between services

### 3. **Soft Delete Pattern**

```typescript
@prop({ default: false })
public isDeleted!: boolean;

@prop()
public deletedAt?: Date;

@prop()
public deletedBy?: string;
```

- **Purpose**: Preserve audit trail while "deleting" users
- **Benefits**: Compliance, data recovery, referential integrity
- **Usage**: Filter queries with `{ isDeleted: { $ne: true } }`

### 4. **Enhanced Indexing**

```typescript
@index({ identityId: 1 }, { unique: true })
@index({ organizationId: 1, isActive: 1 })
@index({ userTypeCode: 1, editionCode: 1 })
```

- **Purpose**: Optimize common query patterns
- **Benefits**: Faster lookups, better performance at scale
- **Usage**: Efficient queries for business logic and reporting

### 5. **Profile Completion Tracking**

```typescript
@prop({ default: false })
public profileCompleted!: boolean;

@prop()
public profileCompletedAt?: Date;
```

- **Purpose**: Track user onboarding progress
- **Benefits**: UX improvements, analytics, compliance
- **Usage**: Guide users through setup process

## Cross-Service Data Flow

### User Creation Flow

```mermaid
sequenceDiagram
    participant Client
    participant IdP as Identity Service
    participant Finance as Finance Service
    participant Event as Event Bus

    Client->>IdP: Register User
    IdP->>IdP: Create Identity Record
    IdP->>Event: Publish UserCreated Event
    Event->>Finance: UserCreated Event
    Finance->>Finance: Create User Record with identityId
    Finance->>IdP: Confirm User Created
    IdP->>IdP: Update profileCompletedAt
```

### Data Synchronization Strategy

```typescript
// Example sync service
export class UserSyncService {
	async syncUserFromIdentity(identityId: string) {
		try {
			// Get latest data from Identity service
			const identityUser = await identityService.getUser(identityId);

			// Update Finance user record
			await UserModel.findOneAndUpdate(
				{ identityId },
				{
					email: identityUser.email,
					isActive: identityUser.isActive,
					identityLastSyncAt: new Date(),
					identitySyncStatus: 'synced'
				}
			);
		} catch (error) {
			// Mark sync as failed
			await UserModel.findOneAndUpdate({ identityId }, { identitySyncStatus: 'failed' });
			throw error;
		}
	}
}
```
