Alright <PERSON><PERSON>, focusing on boosting your CLI prowess with Bun and ESLint, here are some _new_ and useful commands for your `package.json` scripts, specifically designed to give you more control and insight into your codebase and tests.

These commands omit any previous ones we've discussed and add more granular control, especially for your Bun tests, referencing the structure you showed in your screenshot.

### New ESLint and Bun Test Commands

| Command Script | Description |
| :-- | :-- |
| `lint:dry-fix` | Performs a "dry run" of the `fix` operation for ESLint. It shows you what changes ESLint _would_ make without actually writing them to disk. Useful for reviewing potential automatic fixes before committing. |
| `lint:output:json` | Runs ESLint and outputs the results in a machine-readable JSON format. This is excellent for integrating with other tools or for detailed, programmatic error parsing in CI/CD. |
| `lint:cache` | Runs ESLint using its caching mechanism. Subsequent runs will be significantly faster as ESLint only re-lints changed files. |
| `lint:report:html` | Generates an HTML report of your ESLint results, providing a comprehensive and navigable overview of all issues in a web browser. |
| `test:suite:auth` | Runs all tests within the `Authentication Validation Tests` and `Registration & Login Auth Tests` suites, filtering by a common pattern. Useful for focusing on specific functional areas. |
| `test:specific-it` | Runs a single, specific `it` test block by matching its name. This is powerful for quickly re-running a failing test during development. |
| `test:token-val` | Specifically targets and runs tests within the `Token Validation` suite. |
| `test:debug` | Runs your Bun tests with the Node.js/Bun debugger attached, pausing execution at the start. You can then connect your debugger (e.g., VS Code) to step through your tests. |
| `test:bail` | Runs your Bun tests, but will stop immediately after the first test failure. Useful for quickly identifying the first point of failure without running all subsequent tests. |
| `test:failed` | Reruns only the tests that failed in the immediately preceding `bun test` run. Extremely convenient for rapid iteration on bug fixes. |
| `test:todo` | Lists all tests in your project that are marked as `.todo()`. This helps you keep track of planned but not yet implemented tests. |
| `test:focus` | Runs only tests that have been explicitly marked with `.only()` in your code. Essential for focusing on a single test or suite during development. |

### Updated `package.json` Script Examples:

```json
{
	"name": "introspection.finance.client",
	"version": "1.0.0",
	"private": true,
	"scripts": {
		"dev": "bun --hot run src/index.ts",
		"start": "bun run dist/index.js",
		"bun:build": "bun build --target bun --outfile dist/index.js src/index.ts",
		"bun:test": "bun test",
		"bun:test:watch": "bun test --watch",
		"bun:test:coverage": "bun test --coverage",
		"bun:install": "bun install",
		"bun:add": "bun add",
		"bun:remove": "bun remove",
		"bun:x": "bun x",
		"check:types": "npx tsc --noEmit",
		"lint": "eslint .",
		"lint:fix": "eslint . --fix",
		"lint:strict": "eslint . --max-warnings 0",
		"lint:errors-only": "eslint . --quiet",
		"lint:dry-fix": "eslint . --fix-dry-run --format compact",
		"lint:output:json": "eslint . --format json",
		"lint:cache": "eslint . --cache",
		"lint:report:html": "eslint . --format html --output-file eslint-report.html",
		"check:format": "prettier --check .",
		"format:fix": "prettier --write .",
		"test:suite:auth": "bun test --filter \"Authentication Validation|Registration & Login Auth\"",
		"test:specific-it": "bun test --grep \"Should successfully register a new user\"",
		"test:token-val": "bun test --filter \"Token Validation\"",
		"test:debug": "bun --inspect-brk test",
		"test:bail": "bun test --bail",
		"test:failed": "bun test --only-failed",
		"test:todo": "bun test --todo",
		"test:focus": "bun test --only"
	},
	"devDependencies": {
		"@types/bun": "^1.0.0",
		"eslint": "^8.0.0",
		"prettier": "^3.0.0",
		"typescript": "^5.0.0"
	}
}
```

**How to use `test:specific-it` and `test:suite:auth`:** For `test:specific-it`, you'll likely want to modify the `--grep` pattern with the exact title of the `it` block you're working on. For `test:suite:auth`, I used a regex to match both `describe` blocks visible in your screenshot. You can adjust these `--filter` or `--grep` patterns to target any specific test suite or individual test you need.

These scripts should give you much finer control over your development workflow, helping you leverage the CLI more effectively for your Excelytics project!

---

Okay Reza, let's dive into some _new_ and more specialized ESLint and Bun test commands for your `package.json` scripts, directly leveraging your `eslint.config.mjs` and your desire to master the CLI. I'll omit the commands we've already covered and focus on adding further control and diagnostic capabilities.

Given your `eslint.config.mjs` clearly uses `perfectionist` for sorting imports and named imports (both fixable errors), and `eslint-plugin-import` for import style, we can highlight scripts that address these.

### New ESLint and Bun Test Commands

| Command Script | Description |
| :-- | :-- |
| `lint:fix-style-imports` | **ESLint Specific Fix:** Runs ESLint and applies all automatic fixes. Given your `eslint.config.mjs` includes rules like `perfectionist/sort-imports`, `perfectionist/sort-named-imports`, `import/newline-after-import`, `prefer-const`, and `no-var` (all fixable), this script will specifically address formatting, import organization, and modern JavaScript syntax, ensuring consistent style across your codebase. This is a conceptual replacement for a generic `lint:fix` that emphasizes the style-related fixes in your specific config. |
| `lint:json-report` | **ESLint Advanced Reporting:** Runs ESLint and outputs the results in a machine-readable JSON format. This is invaluable for integrating with custom reporting tools, building dashboards, or parsing linting results programmatically in CI/CD pipelines for your microservices. |
| `lint:compact-output` | **ESLint Output Control:** Runs ESLint and uses the "compact" formatter. This provides a very concise, one-line-per-issue output, which can be useful when you want to quickly scan for issues without the verbose context of the default "stylish" formatter. |
| `lint:codeframe-output` | **ESLint Detailed Output:** Runs ESLint and uses the "codeframe" formatter. This output highlights the exact problematic code snippet directly in your terminal, including a few lines of context around the error. Great for quick visual debugging of linting issues without opening your editor. |
| `lint:cache-purge` | **ESLint Cache Management:** Manually deletes the `.eslintcache` file. This forces ESLint to re-lint _all_ files from scratch on its next run, which can be useful for debugging issues where ESLint might be using outdated cache data, or after significant configuration changes that weren't fully picked up. |
| `test:file` | **Bun Test Granular Control:** Runs tests specifically within a single test file. You'll replace `./path/to/your/file.test.ts` with the actual path to the test file you want to run. This is extremely useful when you're focusing on debugging or developing tests for a particular module or feature. Example: `bun run test:file src/introspection.finance.identity/tests/login.test.ts` |
| `test:shard` | **Bun Test for CI/CD:** Runs a specific "shard" of your test suite. This command is designed for CI/CD environments where you want to parallelize your tests across multiple agents to speed up your pipeline. For example, `bun run test:shard 1/3` would run the first third of your tests. You'd typically set up multiple CI jobs, each running a different shard. |
| `test:list-names` | **Bun Test Discovery:** Lists all discovered test suite and individual test names without actually running any tests. This is a handy way to get an overview of your test structure, verify test discovery, or grab exact test names for use with `--grep` or `--filter` flags in other test commands. |

### Updated `package.json` Scripts Example:

```json
{
	"name": "introspection.finance.client",
	"version": "1.0.0",
	"private": true,
	"scripts": {
		"dev": "bun --hot run src/index.ts",
		"start": "bun run dist/index.js",
		"bun:build": "bun build --target bun --outfile dist/index.js src/index.ts",
		"bun:test": "bun test",
		"bun:test:watch": "bun test --watch",
		"bun:test:coverage": "bun test --coverage",
		"bun:install": "bun install",
		"bun:add": "bun add",
		"bun:remove": "bun remove",
		"bun:x": "bun x",
		"check:types": "npx tsc --noEmit",
		"lint": "eslint .",
		"lint:fix": "eslint . --fix",
		"lint:strict": "eslint . --max-warnings 0",
		"lint:errors-only": "eslint . --quiet",
		"lint:fix-style-imports": "eslint . --fix",
		"lint:json-report": "eslint . --format json",
		"lint:compact-output": "eslint . --format compact",
		"lint:codeframe-output": "eslint . --format codeframe",
		"lint:cache-purge": "rm -f .eslintcache",
		"check:format": "prettier --check .",
		"format:fix": "prettier --write .",
		"test:suite:auth": "bun test --filter \"Authentication Validation|Registration & Login Auth\"",
		"test:specific-it": "bun test --grep \"Should successfully register a new user\"",
		"test:token-val": "bun test --filter \"Token Validation\"",
		"test:debug": "bun --inspect-brk test",
		"test:bail": "bun test --bail",
		"test:failed": "bun test --only-failed",
		"test:todo": "bun test --todo",
		"test:focus": "bun test --only",
		"test:file": "bun test",
		"test:shard": "bun test",
		"test:list-names": "bun test --list"
	},
	"devDependencies": {
		"@types/bun": "^1.0.0",
		"eslint": "^8.0.0",
		"prettier": "^3.0.0",
		"typescript": "^5.0.0"
	}
}
```

**Important Note for `test:file` and `test:shard`:** For `test:file`, you'll need to explicitly add the path to the test file when you run the command, e.g., `bun run test:file src/tests/auth.test.ts`. For `test:shard`, you'll append the shard arguments, e.g., `bun run test:shard --shard 1/2`. The `bun test` command directly accepts these arguments after the script name.

These commands will give you much more flexibility and insight into your code quality and testing processes!
