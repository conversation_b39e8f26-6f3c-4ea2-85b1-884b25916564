# MongoDB Session Store Documentation

## Overview

The MongoDB session store (`connect-mongo`) is a session storage solution that persists user sessions in MongoDB instead of memory. This is essential for production environments where sessions need to survive server restarts and be shared across multiple server instances.

## Why MongoDB Session Store?

### Problems with Default Memory Store
- **Memory Leaks**: Sessions accumulate in memory over time
- **Server Restart**: All sessions lost when server restarts
- **Scalability**: Cannot share sessions across multiple server instances
- **Production Warning**: Express explicitly warns against using MemoryStore in production

### Benefits of MongoDB Session Store
- **Persistence**: Sessions survive server restarts
- **Scalability**: Multiple server instances can share the same session store
- **Automatic Cleanup**: TTL (Time To Live) indexing automatically removes expired sessions
- **Security**: Sessions can be encrypted at rest
- **Monitoring**: Easy to monitor and analyze session data

## Configuration in Our Identity Service

### Environment-Based Configuration
```typescript
// Use same connection logic as main database
let mongoUrl: string;
if (env_idp.VPN) {
    mongoUrl = env_idp.MONGODB_URI_UNRAID!;
} else {
    mongoUrl = env_idp.MONGODB_URI;
}
```

### Session Store Setup
```typescript
sessionStore = MongoStore.create({
    mongoUrl: mongoUrl,
    collectionName: 'sessions_idp',           // Dedicated collection for IdP sessions
    ttl: 14 * 24 * 60 * 60,                  // 14 days TTL
    touchAfter: 24 * 3600,                   // Update every 24 hours
    mongoOptions: {
        serverSelectionTimeoutMS: 5000,       // 5 second timeout
        connectTimeoutMS: 10000,              // 10 second connection timeout
    }
});
```

## Session Lifecycle

### 1. Session Creation
```mermaid
sequenceDiagram
    participant Client
    participant Express
    participant MongoStore
    participant MongoDB
    
    Client->>Express: First request (no session cookie)
    Express->>MongoStore: Create new session
    MongoStore->>MongoDB: INSERT into sessions_idp
    MongoDB-->>MongoStore: Session created
    MongoStore-->>Express: Session ID
    Express-->>Client: Set-Cookie: excelytics.idp.sid=...
```

### 2. Session Retrieval
```mermaid
sequenceDiagram
    participant Client
    participant Express
    participant MongoStore
    participant MongoDB
    
    Client->>Express: Request with session cookie
    Express->>MongoStore: Load session by ID
    MongoStore->>MongoDB: FIND in sessions_idp
    MongoDB-->>MongoStore: Session data
    MongoStore-->>Express: Session object
    Express->>Express: Process request with session
```

### 3. Session Update (Touch)
```mermaid
sequenceDiagram
    participant Express
    participant MongoStore
    participant MongoDB
    
    Note over Express: Session accessed after touchAfter period
    Express->>MongoStore: Touch session (update lastAccess)
    MongoStore->>MongoDB: UPDATE session expires field
    MongoDB-->>MongoStore: Session updated
```

### 4. Automatic Cleanup
```mermaid
sequenceDiagram
    participant MongoDB
    participant TTLIndex
    
    Note over MongoDB: Background process runs every 60 seconds
    MongoDB->>TTLIndex: Check for expired sessions
    TTLIndex->>MongoDB: DELETE expired documents
    Note over MongoDB: Sessions older than TTL automatically removed
```

## Session Document Structure

### Example Session Document
```javascript
{
  "_id": "abc123sessionid456def",
  "expires": ISODate("2025-07-08T22:55:00.000Z"),
  "session": {
    "cookie": {
      "originalMaxAge": 3600000,
      "expires": "2025-06-25T01:55:00.000Z",
      "secure": false,
      "httpOnly": true,
      "path": "/",
      "sameSite": "lax"
    },
    // Custom session data can be stored here
    "userId": "user_123",
    "clientId": "client_456",
    "lastActivity": "2025-06-24T22:55:00.000Z"
  }
}
```

### TTL Index
MongoDB automatically creates a TTL index on the `expires` field:
```javascript
db.sessions_idp.createIndex({ "expires": 1 }, { expireAfterSeconds: 0 })
```

## Use Cases for Identity Provider

### 1. OAuth/OIDC Flows
```typescript
// Store OAuth state during authorization flow
req.session.oauthState = generateRandomState();
req.session.redirectUri = req.query.redirect_uri;
req.session.clientId = req.query.client_id;
```

### 2. Multi-Step Authentication
```typescript
// Store partial authentication state
req.session.partialAuth = {
    userId: user.id,
    step: 'mfa_required',
    timestamp: Date.now()
};
```

### 3. Remember Me Functionality
```typescript
// Extend session for "remember me"
if (req.body.rememberMe) {
    req.session.cookie.maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
}
```

### 4. Cross-Service Session Sharing
```typescript
// Store user context for other microservices
req.session.userContext = {
    userId: user.id,
    clientOrigin: user.clientOrigin,
    permissions: user.permissions,
    lastLogin: user.lastLogin
};
```

### 5. Security Tracking
```typescript
// Track security-related events
req.session.security = {
    loginAttempts: 0,
    lastFailedLogin: null,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent')
};
```

## Configuration Options

### TTL (Time To Live)
- **Purpose**: Automatic session expiration
- **Our Setting**: 14 days (1,209,600 seconds)
- **MongoDB Behavior**: Automatically deletes expired sessions

### Touch After
- **Purpose**: Avoid unnecessary database writes
- **Our Setting**: 24 hours (86,400 seconds)
- **Behavior**: Only updates session if accessed after this period

### Collection Name
- **Purpose**: Organize sessions by service
- **Our Setting**: `sessions_idp`
- **Benefit**: Separate IdP sessions from other services

## Security Considerations

### Session Cookie Configuration
```typescript
cookie: {
    secure: env_idp.ENV !== EnumEnv.Development,    // HTTPS only in production
    httpOnly: true,                                 // Prevent XSS access
    maxAge: sessionMaxAgeMs,                        // 1 hour default
    sameSite: env_idp.ENV === EnumEnv.Development ? 'lax' : 'strict',
    path: '/',
    domain: env_idp.ENV !== EnumEnv.Development 
        ? URIConstants.Excelytics_Domain 
        : undefined
}
```

### Optional Encryption
```typescript
// Can be enabled for sensitive data
crypto: {
    secret: env_idp.SESSION_STORE_CRYPTO_SECRET,
    algorithm: 'aes-256-gcm'
}
```

## Monitoring and Maintenance

### Session Analytics
```javascript
// Query active sessions
db.sessions_idp.countDocuments({ expires: { $gt: new Date() } })

// Query sessions by user (if userId stored in session)
db.sessions_idp.find({ "session.userId": "user_123" })

// Query sessions by client
db.sessions_idp.find({ "session.clientId": "client_456" })
```

### Cleanup Monitoring
```javascript
// Check TTL index exists
db.sessions_idp.getIndexes()

// Monitor expired session cleanup
db.sessions_idp.find({ expires: { $lt: new Date() } }).count()
```

## Troubleshooting

### Common Issues

1. **Sessions Not Persisting**
   - Check MongoDB connection
   - Verify collection permissions
   - Check TTL configuration

2. **Memory Leaks**
   - Ensure using MongoStore, not MemoryStore
   - Monitor session collection size
   - Verify TTL index is working

3. **Connection Timeouts**
   - Adjust `serverSelectionTimeoutMS`
   - Check network connectivity to MongoDB
   - Verify MongoDB is running

### Debug Commands
```bash
# Check MongoDB connection
mongosh "*****************************:port/database"

# View session collection
db.sessions_idp.find().limit(5).pretty()

# Check TTL index
db.sessions_idp.getIndexes()

# Monitor session count
db.sessions_idp.countDocuments()
```

## Best Practices

1. **Use Dedicated Collection**: Separate IdP sessions from other data
2. **Set Appropriate TTL**: Balance security with user experience
3. **Monitor Session Size**: Avoid storing large objects in sessions
4. **Use Touch After**: Reduce unnecessary database writes
5. **Encrypt Sensitive Data**: Use crypto option for sensitive session data
6. **Regular Monitoring**: Monitor session collection size and performance
