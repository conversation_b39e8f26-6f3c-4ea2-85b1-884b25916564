> Created on 25.06.2025 - Generated by Augment Copilot Agent with Context of IdP's latest infrastructure

# Introspection Finance Identity Provider (IdP) API Documentation

## Table of Contents
1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [Authentication Flow](#authentication-flow)
4. [API Endpoints](#api-endpoints)
5. [Data Types & Formats](#data-types--formats)
6. [Security Architecture](#security-architecture)
7. [Infrastructure Diagrams](#infrastructure-diagrams)
8. [Error Handling](#error-handling)
9. [Testing & Validation](#testing--validation)

## Overview

The Introspection Finance Identity Provider (IdP) is a secure, JWT-based authentication and authorization service designed for the Excelytics microservices ecosystem. It provides centralized user management, role-based access control, and secure token generation for all services in the platform.

### Key Features
- 🔐 JWT-based authentication with access and refresh tokens
- 👥 Role-based authorization (user, admin)
- 🛡️ Comprehensive security middleware
- 🔄 Token refresh mechanism
- 📊 Rate limiting and CORS protection
- 🏥 Health monitoring endpoints
- 🧹 Admin user management capabilities

## System Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Applications"
        FE[Frontend Client<br/>excelytics.client]
        BE[Backend Services<br/>excelytics.finance]
        CALC[Calculation Engine<br/>excelytics.calc]
    end
    
    subgraph "Identity Provider"
        API[IdP API Server<br/>Port 6002]
        AUTH[Authentication<br/>Middleware]
        RBAC[Role-Based<br/>Access Control]
        TOKEN[Token Management<br/>JWT Service]
    end
    
    subgraph "Data Layer"
        MONGO[(MongoDB<br/>User Data)]
        REDIS[(Redis<br/>Sessions)]
    end
    
    subgraph "Security Layer"
        HELMET[Helmet<br/>Security Headers]
        CORS[CORS<br/>Protection]
        RATE[Rate Limiting<br/>Protection]
    end
    
    FE --> API
    BE --> API
    CALC --> API
    
    API --> AUTH
    AUTH --> RBAC
    AUTH --> TOKEN
    
    API --> MONGO
    API --> REDIS
    
    API --> HELMET
    API --> CORS
    API --> RATE
    
    classDef client fill:#e1f5fe
    classDef idp fill:#f3e5f5
    classDef data fill:#e8f5e8
    classDef security fill:#fff3e0
    
    class FE,BE,CALC client
    class API,AUTH,RBAC,TOKEN idp
    class MONGO,REDIS data
    class HELMET,CORS,RATE security
```

### Microservices Communication

```mermaid
sequenceDiagram
    participant Client as Client App
    participant IdP as Identity Provider
    participant Finance as Finance Service
    participant Calc as Calculation Engine
    participant DB as MongoDB
    
    Note over Client,DB: User Registration & Login
    Client->>IdP: POST /auth/register
    IdP->>DB: Create User
    DB-->>IdP: User Created
    IdP-->>Client: Access + Refresh Tokens
    
    Note over Client,DB: Service Authentication
    Client->>Finance: GET /api/data (with token)
    Finance->>IdP: POST /verify-access-token
    IdP-->>Finance: Token Valid + User Info
    Finance-->>Client: Protected Data
    
    Note over Client,DB: Cross-Service Communication
    Finance->>Calc: POST /calculate (with token)
    Calc->>IdP: POST /verify-access-token
    IdP-->>Calc: Token Valid + Permissions
    Calc-->>Finance: Calculation Results
```

## Authentication Flow

### Complete Authentication Lifecycle

```mermaid
flowchart TD
    START([User Starts]) --> REG{New User?}

    REG -->|Yes| REGISTER["POST /auth/register
    - email, password
    - clientId, clientOrigin"]
    REG -->|No| LOGIN["POST /auth/login
    - email, password"]

    REGISTER --> VALIDATE["Validate Input
    - Email format
    - Password strength
    - Client validation"]
    LOGIN --> VALIDATE

    VALIDATE --> HASH["Hash Password
    bcrypt"]
    HASH --> STORE["Store in MongoDB
    - User document
    - Client document"]

    STORE --> GENERATE["Generate JWT Tokens
    - Access Token (30min)
    - Refresh Token (7 days)"]

    GENERATE --> RETURN["Return Tokens
    + User Info"]

    RETURN --> USE["Use Access Token
    Authorization: Bearer"]

    USE --> EXPIRED{Token Expired?}
    EXPIRED -->|No| CONTINUE[Continue Using]
    EXPIRED -->|Yes| REFRESH["POST /auth/refresh
    with refresh token"]

    REFRESH --> NEW_ACCESS["Generate New
    Access Token"]
    NEW_ACCESS --> USE

    CONTINUE --> LOGOUT["POST /auth/logout
    Invalidate tokens"]
    LOGOUT --> END([Session End])

    classDef process fill:#e3f2fd
    classDef decision fill:#fff3e0
    classDef endpoint fill:#f3e5f5
    classDef security fill:#e8f5e8

    class VALIDATE,HASH,STORE,GENERATE process
    class REG,EXPIRED decision
    class REGISTER,LOGIN,REFRESH,LOGOUT endpoint
    class USE,CONTINUE security
```

### Token Structure & Validation

```mermaid
graph LR
    subgraph "JWT Token Structure"
        HEADER["Header
        alg: HS256
        typ: JWT"]
        PAYLOAD["Payload
        userId, email
        clientId, permissions
        exp, iat"]
        SIGNATURE["Signature
        HMAC SHA256"]
    end

    subgraph "Token Types"
        ACCESS["Access Token
        30 minutes
        API access"]
        REFRESH["Refresh Token
        7 days
        Token renewal"]
    end

    subgraph "Validation Process"
        VERIFY[Verify Signature]
        CHECK_EXP[Check Expiration]
        EXTRACT[Extract User Info]
        AUTHORIZE[Check Permissions]
    end

    HEADER --> VERIFY
    PAYLOAD --> CHECK_EXP
    SIGNATURE --> VERIFY

    ACCESS --> VERIFY
    REFRESH --> VERIFY

    VERIFY --> EXTRACT
    CHECK_EXP --> EXTRACT
    EXTRACT --> AUTHORIZE

    classDef token fill:#e1f5fe
    classDef process fill:#f3e5f5

    class HEADER,PAYLOAD,SIGNATURE,ACCESS,REFRESH token
    class VERIFY,CHECK_EXP,EXTRACT,AUTHORIZE process
```

## API Endpoints

### Authentication Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/auth/register` | Register new user | No |
| POST | `/auth/login` | User login | No |
| POST | `/auth/refresh` | Refresh access token | Refresh Token |
| POST | `/auth/logout` | Logout user | Access Token |
| POST | `/verify-access-token` | Validate token | No |

### User Management Endpoints

| Method | Endpoint | Description | Auth Required | Permissions |
|--------|----------|-------------|---------------|-------------|
| GET | `/identity/email/{email}` | Get user by email | Access Token | Admin |
| DELETE | `/identity/email/{email}` | Delete user by email | Access Token | Admin |
| GET | `/identity/users` | List all users | Access Token | Admin |
| PUT | `/identity/users/{id}` | Update user | Access Token | Admin/Self |

### Health & Monitoring

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/health` | Basic health check | No |
| GET | `/health/all` | Detailed health status | No |

## Data Types & Formats

### User Registration Request

```typescript
interface RegisterRequest {
  email: string;           // Valid email format
  password: string;        // Min 8 chars, complexity rules
  clientId?: string;       // Optional client identifier
  clientOrigin: ClientOrigin; // Enum: WEB, MOBILE, API
}

enum ClientOrigin {
  WEB = 1,
  MOBILE = 2,
  API = 3
}
```

### Authentication Response

```typescript
interface AuthResponse {
  success: boolean;
  message: string;
  token: string;          // JWT Access Token
  refreshToken: string;   // JWT Refresh Token
  tokenPayload: {
    userId: string;
    email: string;
    clientId: string;
    clientOrigin: number;
    permissions: string[]; // ["user", "admin"]
    issuedAt: string;     // ISO date
    expiresAt: string;    // ISO date
  };
}
```

### Token Payload Structure

```typescript
interface TokenPayload {
  userId: string;         // MongoDB ObjectId
  email: string;          // User email
  clientId: string;       // Client identifier
  clientOrigin: number;   // Client type
  clientPath: string;     // Service path
  isActive: boolean;      // User status
  permissions: string[];  // Role permissions
  tokenType: "access" | "refresh";
  issuedAt: number;      // Unix timestamp
  iat: number;           // JWT issued at
  exp: number;           // JWT expires at
}
```

### Error Response Format

```typescript
interface ErrorResponse {
  success: false;
  message: string;
  error: {
    code: string;         // Error code (UNAUTHORIZED, FORBIDDEN, etc.)
    message: string;      // Human readable message
    type?: string;        // Error type classification
    details?: any;        // Additional error details
  };
}
```

## Security Architecture

### Multi-Layer Security Model

```mermaid
graph TB
    subgraph "External Layer"
        NGINX["NGINX Gateway
        SSL Termination
        Load Balancing"]
        FIREWALL["Firewall Rules
        Port Restrictions"]
    end

    subgraph "Application Security"
        HELMET["Helmet.js
        Security Headers
        XSS Protection"]
        CORS["CORS Policy
        Origin Validation
        Preflight Handling"]
        RATE["Rate Limiting
        Login Attempts
        API Calls"]
    end

    subgraph "Authentication Layer"
        JWT["JWT Tokens
        HMAC SHA256
        Short Expiry"]
        REFRESH["Refresh Tokens
        Secure Storage
        Rotation"]
        VALIDATION["Token Validation
        Signature Check
        Expiry Check"]
    end

    subgraph "Authorization Layer"
        RBAC["Role-Based Access
        User/Admin Roles
        Permission Matrix"]
        MIDDLEWARE["Auth Middleware
        Request Interception
        Token Extraction"]
    end

    subgraph "Data Security"
        BCRYPT["Password Hashing
        bcrypt + salt
        Cost Factor 12"]
        MONGO_AUTH["MongoDB Auth
        User Credentials
        Connection Security"]
        REDIS_AUTH["Redis Auth
        Session Security
        TTL Management"]
    end

    NGINX --> HELMET
    FIREWALL --> NGINX
    HELMET --> CORS
    CORS --> RATE
    RATE --> JWT
    JWT --> REFRESH
    REFRESH --> VALIDATION
    VALIDATION --> RBAC
    RBAC --> MIDDLEWARE
    MIDDLEWARE --> BCRYPT
    BCRYPT --> MONGO_AUTH
    MONGO_AUTH --> REDIS_AUTH

    classDef external fill:#ffebee
    classDef app fill:#e8f5e8
    classDef auth fill:#e3f2fd
    classDef authz fill:#fff3e0
    classDef data fill:#f3e5f5

    class NGINX,FIREWALL external
    class HELMET,CORS,RATE app
    class JWT,REFRESH,VALIDATION auth
    class RBAC,MIDDLEWARE authz
    class BCRYPT,MONGO_AUTH,REDIS_AUTH data
```

### Permission Matrix

```mermaid
graph LR
    subgraph "User Roles"
        USER["User Role
        Basic Access"]
        ADMIN["Admin Role
        Full Access"]
    end

    subgraph "Permissions"
        READ_SELF[Read Own Data]
        UPDATE_SELF[Update Own Data]
        READ_ALL[Read All Users]
        DELETE_USER[Delete Users]
        MANAGE_ROLES[Manage Roles]
        SYSTEM_CONFIG[System Config]
    end

    USER --> READ_SELF
    USER --> UPDATE_SELF

    ADMIN --> READ_SELF
    ADMIN --> UPDATE_SELF
    ADMIN --> READ_ALL
    ADMIN --> DELETE_USER
    ADMIN --> MANAGE_ROLES
    ADMIN --> SYSTEM_CONFIG

    classDef role fill:#e3f2fd
    classDef permission fill:#e8f5e8

    class USER,ADMIN role
    class READ_SELF,UPDATE_SELF,READ_ALL,DELETE_USER,MANAGE_ROLES,SYSTEM_CONFIG permission
```

### Security Headers Configuration

```mermaid
graph TD
    subgraph "Helmet.js Security Headers"
        CSP["Content Security Policy
        script-src 'self'
        object-src 'none'"]
        HSTS["HTTP Strict Transport
        max-age=31536000
        includeSubDomains"]
        FRAME["X-Frame-Options
        DENY
        Clickjacking Protection"]
        CONTENT["X-Content-Type-Options
        nosniff
        MIME Sniffing Protection"]
        XSS["X-XSS-Protection
        1; mode=block
        XSS Filter"]
        REFERRER["Referrer-Policy
        strict-origin-when-cross-origin"]
        PERMISSIONS["Permissions-Policy
        camera=(), microphone=()"]
    end

    REQUEST[Incoming Request] --> CSP
    CSP --> HSTS
    HSTS --> FRAME
    FRAME --> CONTENT
    CONTENT --> XSS
    XSS --> REFERRER
    REFERRER --> PERMISSIONS
    PERMISSIONS --> RESPONSE[Secure Response]

    classDef header fill:#fff3e0
    classDef flow fill:#e8f5e8

    class CSP,HSTS,FRAME,CONTENT,XSS,REFERRER,PERMISSIONS header
    class REQUEST,RESPONSE flow
```

## Infrastructure Diagrams

### Deployment Architecture

```mermaid
graph TB
    subgraph "Unraid Server Infrastructure"
        subgraph "Docker Containers"
            IDP_CONTAINER["IdP Service
            Node.js + Express
            Port 6002"]
            MONGO_CONTAINER["MongoDB
            User Data
            Port 27017"]
            REDIS_CONTAINER["Redis
            Sessions
            Port 6379"]
            NGINX_CONTAINER["NGINX Gateway
            Reverse Proxy
            Port 80/443"]
        end

        subgraph "Volumes"
            MONGO_VOL["MongoDB Data
            /mnt/user/appdata/mongodb"]
            REDIS_VOL["Redis Data
            /mnt/user/appdata/redis"]
            NGINX_VOL["NGINX Config
            /mnt/user/appdata/nginx"]
            LOGS_VOL["Application Logs
            /mnt/user/logs"]
        end
    end

    subgraph "External Access"
        INTERNET[Internet]
        CLIENTS["Client Applications
        Frontend, Backend, Calc"]
    end

    subgraph "Network"
        DOCKER_NET["Docker Network
        excelytics-network"]
        HOST_NET["Host Network
        ************"]
    end

    INTERNET --> NGINX_CONTAINER
    CLIENTS --> NGINX_CONTAINER
    NGINX_CONTAINER --> IDP_CONTAINER
    IDP_CONTAINER --> MONGO_CONTAINER
    IDP_CONTAINER --> REDIS_CONTAINER

    IDP_CONTAINER -.-> LOGS_VOL
    MONGO_CONTAINER -.-> MONGO_VOL
    REDIS_CONTAINER -.-> REDIS_VOL
    NGINX_CONTAINER -.-> NGINX_VOL

    DOCKER_NET --> HOST_NET

    classDef container fill:#e3f2fd
    classDef volume fill:#e8f5e8
    classDef network fill:#fff3e0
    classDef external fill:#ffebee

    class IDP_CONTAINER,MONGO_CONTAINER,REDIS_CONTAINER,NGINX_CONTAINER container
    class MONGO_VOL,REDIS_VOL,NGINX_VOL,LOGS_VOL volume
    class DOCKER_NET,HOST_NET network
    class INTERNET,CLIENTS external
```

### Service Discovery & Communication

```mermaid
graph LR
    subgraph "Service Mesh"
        GATEWAY["NGINX Gateway
        excelytics.gateway"]
        IDP["Identity Provider
        excelytics.identity
        Port 6002"]
        FINANCE["Finance Service
        excelytics.finance
        Port 6001"]
        CALC["Calculation Engine
        excelytics.calc
        Port 6003"]
        CLIENT["Frontend Client
        excelytics.client
        Port 4200"]
    end

    subgraph "Data Services"
        MONGO[("MongoDB
        Primary Database")]
        REDIS[("Redis
        Cache & Sessions")]
    end

    subgraph "External"
        USERS[End Users]
        APIS[External APIs]
    end

    USERS --> GATEWAY
    GATEWAY --> CLIENT
    GATEWAY --> IDP
    GATEWAY --> FINANCE
    GATEWAY --> CALC

    CLIENT --> IDP
    FINANCE --> IDP
    CALC --> IDP

    IDP --> MONGO
    IDP --> REDIS
    FINANCE --> MONGO
    CALC --> REDIS

    FINANCE --> APIS

    classDef service fill:#e3f2fd
    classDef data fill:#e8f5e8
    classDef external fill:#ffebee
    classDef gateway fill:#fff3e0

    class IDP,FINANCE,CALC,CLIENT service
    class MONGO,REDIS data
    class USERS,APIS external
    class GATEWAY gateway
```

### Network Security & Firewall Rules

```mermaid
graph TB
    subgraph "Internet"
        EXTERNAL["External Traffic
        HTTPS Only"]
    end

    subgraph "DMZ Layer"
        NGINX_DMZ["NGINX Reverse Proxy
        Port 443/80
        SSL Termination"]
    end

    subgraph "Application Layer"
        IDP_APP["IdP Service
        Port 6002
        Internal Only"]
        FINANCE_APP["Finance Service
        Port 6001
        Internal Only"]
        CALC_APP["Calc Service
        Port 6003
        Internal Only"]
    end

    subgraph "Data Layer"
        MONGO_DB[("MongoDB
        Port 27017
        Auth Required")]
        REDIS_DB[("Redis
        Port 6379
        Auth Required")]
    end

    EXTERNAL -->|HTTPS 443| NGINX_DMZ
    NGINX_DMZ -->|HTTP 6002| IDP_APP
    NGINX_DMZ -->|HTTP 6001| FINANCE_APP
    NGINX_DMZ -->|HTTP 6003| CALC_APP

    IDP_APP -->|27017| MONGO_DB
    IDP_APP -->|6379| REDIS_DB
    FINANCE_APP -->|27017| MONGO_DB
    CALC_APP -->|6379| REDIS_DB

    classDef external fill:#ffebee
    classDef dmz fill:#fff3e0
    classDef app fill:#e3f2fd
    classDef data fill:#e8f5e8

    class EXTERNAL external
    class NGINX_DMZ dmz
    class IDP_APP,FINANCE_APP,CALC_APP app
    class MONGO_DB,REDIS_DB data
```

## Error Handling

### Global Error Handler Flow

```mermaid
flowchart TD
    ERROR[Error Occurs] --> CATCH[Global Error Handler]

    CATCH --> CHECK_TYPE{Error Type?}

    CHECK_TYPE -->|BaseError| KNOWN["Known Error
    Structured Response"]
    CHECK_TYPE -->|ValidationError| VALIDATION["Validation Error
    Field Details"]
    CHECK_TYPE -->|MongoError| DATABASE["Database Error
    Safe Message"]
    CHECK_TYPE -->|Unknown| UNKNOWN["Unknown Error
    Generic Message"]

    KNOWN --> LOG_KNOWN["Log Error Details
    Include Stack Trace"]
    VALIDATION --> LOG_VALIDATION["Log Validation Failure
    Include Field Info"]
    DATABASE --> LOG_DB["Log Database Error
    Redact Sensitive Data"]
    UNKNOWN --> LOG_UNKNOWN["Log Unknown Error
    Full Stack Trace"]

    LOG_KNOWN --> RESPOND_KNOWN["Return Structured
    Error Response"]
    LOG_VALIDATION --> RESPOND_VALIDATION["Return Validation
    Error Response"]
    LOG_DB --> RESPOND_DB["Return Generic
    Database Error"]
    LOG_UNKNOWN --> RESPOND_UNKNOWN["Return Generic
    Server Error"]

    RESPOND_KNOWN --> CLIENT["Client Receives
    Error Response"]
    RESPOND_VALIDATION --> CLIENT
    RESPOND_DB --> CLIENT
    RESPOND_UNKNOWN --> CLIENT

    classDef error fill:#ffebee
    classDef process fill:#e8f5e8
    classDef response fill:#e3f2fd

    class ERROR,CHECK_TYPE error
    class CATCH,LOG_KNOWN,LOG_VALIDATION,LOG_DB,LOG_UNKNOWN process
    class KNOWN,VALIDATION,DATABASE,UNKNOWN,RESPOND_KNOWN,RESPOND_VALIDATION,RESPOND_DB,RESPOND_UNKNOWN,CLIENT response
```

### Error Code Hierarchy

```mermaid
graph TD
    subgraph "Authentication Errors (4xx)"
        UNAUTHORIZED["UNAUTHORIZED
        401
        Missing/Invalid Token"]
        FORBIDDEN["FORBIDDEN
        403
        Insufficient Permissions"]
        INVALID_TOKEN["INVALID_TOKEN
        401
        Malformed JWT"]
        EXPIRED_TOKEN["EXPIRED_TOKEN
        401
        Token Expired"]
    end

    subgraph "Validation Errors (4xx)"
        VALIDATION_ERROR["VALIDATION_ERROR
        400
        Input Validation Failed"]
        MISSING_FIELD["MISSING_FIELD
        400
        Required Field Missing"]
        INVALID_FORMAT["INVALID_FORMAT
        400
        Invalid Data Format"]
    end

    subgraph "Resource Errors (4xx)"
        NOT_FOUND["NOT_FOUND
        404
        Resource Not Found"]
        CONFLICT["CONFLICT
        409
        Resource Conflict"]
        RATE_LIMITED["RATE_LIMITED
        429
        Too Many Requests"]
    end

    subgraph "Server Errors (5xx)"
        INTERNAL_ERROR["INTERNAL_ERROR
        500
        Server Error"]
        DATABASE_ERROR["DATABASE_ERROR
        500
        Database Connection"]
        SERVICE_UNAVAILABLE["SERVICE_UNAVAILABLE
        503
        Service Down"]
    end

    classDef auth fill:#ffebee
    classDef validation fill:#fff3e0
    classDef resource fill:#e8f5e8
    classDef server fill:#f3e5f5

    class UNAUTHORIZED,FORBIDDEN,INVALID_TOKEN,EXPIRED_TOKEN auth
    class VALIDATION_ERROR,MISSING_FIELD,INVALID_FORMAT validation
    class NOT_FOUND,CONFLICT,RATE_LIMITED resource
    class INTERNAL_ERROR,DATABASE_ERROR,SERVICE_UNAVAILABLE server
```

## Testing & Validation

### Test Coverage Architecture

```mermaid
graph TB
    subgraph "Test Layers"
        UNIT["Unit Tests
        Individual Functions
        Mocked Dependencies"]
        INTEGRATION["Integration Tests
        API Endpoints
        Real Database"]
        SECURITY["Security Tests
        Authentication
        Authorization
        Rate Limiting"]
        E2E["End-to-End Tests
        Complete User Flows
        Cross-Service"]
    end

    subgraph "Test Categories"
        AUTH_TESTS["Authentication Tests
        Login/Register/Refresh
        Token Validation"]
        MIDDLEWARE_TESTS["Middleware Tests
        CORS/Helmet/Rate Limiting
        Error Handling"]
        RBAC_TESTS["RBAC Tests
        Permission Checks
        Role Validation"]
        API_TESTS["API Tests
        Endpoint Functionality
        Data Validation"]
    end

    subgraph "Test Infrastructure"
        TEST_DB[("Test Database
        MongoDB Memory")]
        TEST_REDIS[("Test Redis
        In-Memory")]
        MOCK_SERVICES["Mock Services
        External APIs"]
        TEST_HELPERS["Test Helpers
        Auth/Token/Cleanup"]
    end

    UNIT --> AUTH_TESTS
    INTEGRATION --> MIDDLEWARE_TESTS
    SECURITY --> RBAC_TESTS
    E2E --> API_TESTS

    AUTH_TESTS --> TEST_DB
    MIDDLEWARE_TESTS --> TEST_REDIS
    RBAC_TESTS --> MOCK_SERVICES
    API_TESTS --> TEST_HELPERS

    classDef test fill:#e3f2fd
    classDef category fill:#e8f5e8
    classDef infra fill:#fff3e0

    class UNIT,INTEGRATION,SECURITY,E2E test
    class AUTH_TESTS,MIDDLEWARE_TESTS,RBAC_TESTS,API_TESTS category
    class TEST_DB,TEST_REDIS,MOCK_SERVICES,TEST_HELPERS infra
```

### Test Execution Flow

```mermaid
sequenceDiagram
    participant Dev as Developer
    participant Test as Test Suite
    participant Helper as Test Helpers
    participant IdP as IdP Service
    participant DB as Test Database
    participant Admin as Admin Cleanup

    Note over Dev,Admin: Test Setup Phase
    Dev->>Test: Run Security Tests
    Test->>Helper: Setup Test Environment
    Helper->>DB: Initialize Test Database
    Helper->>IdP: Start Test Server

    Note over Dev,Admin: Test Execution Phase
    Test->>Helper: Create Test Users
    Helper->>IdP: Register Users
    IdP->>DB: Store User Data
    Test->>IdP: Execute Test Cases
    IdP-->>Test: Return Results

    Note over Dev,Admin: Cleanup Phase
    Test->>Helper: Create Admin User
    Helper->>IdP: Register Admin
    Helper->>Admin: Admin Cleanup All Users
    Admin->>IdP: Delete All Test Users
    IdP->>DB: Remove User Data
    Admin-->>Helper: Cleanup Complete
    Helper-->>Test: Tests Complete
    Test-->>Dev: Results Report
```

## Configuration Examples

### Environment Configuration

```yaml
# .env.example
NODE_ENV=development
PORT=6002

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/excelytics-identity
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_ACCESS_EXPIRY=30m
JWT_REFRESH_EXPIRY=7d

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=15m
RATE_LIMIT_MAX=100

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:4200,https://excelytics.com
ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=combined
```

### Docker Compose Configuration

```yaml
# docker-compose.yml
version: '3.8'

services:
  excelytics-identity:
    build: .
    ports:
      - "6002:6002"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongodb:27017/excelytics-identity
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mongodb
      - redis
    networks:
      - excelytics-network

  mongodb:
    image: mongo:7
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - excelytics-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - excelytics-network

volumes:
  mongodb_data:
  redis_data:

networks:
  excelytics-network:
    driver: bridge
```

## Usage Examples

### Client Integration Example

```typescript
// Client-side authentication service
class AuthService {
  private baseUrl = 'https://api.excelytics.com/identity';

  async register(email: string, password: string): Promise<AuthResponse> {
    const response = await fetch(`${this.baseUrl}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email,
        password,
        clientOrigin: ClientOrigin.WEB
      })
    });

    return response.json();
  }

  async login(email: string, password: string): Promise<AuthResponse> {
    const response = await fetch(`${this.baseUrl}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });

    return response.json();
  }

  async makeAuthenticatedRequest(url: string, token: string) {
    return fetch(url, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
  }
}
```

### Service-to-Service Authentication

```typescript
// Backend service token validation
class TokenValidator {
  private idpUrl = 'https://api.excelytics.com/identity';

  async validateToken(token: string): Promise<TokenPayload | null> {
    try {
      const response = await fetch(`${this.idpUrl}/verify-access-token`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token })
      });

      const result = await response.json();

      if (result.success && result.data.active) {
        return result.data;
      }

      return null;
    } catch (error) {
      console.error('Token validation failed:', error);
      return null;
    }
  }
}
```

---

## Summary

This Identity Provider (IdP) offers a comprehensive, secure authentication and authorization solution for the Excelytics microservices ecosystem. With JWT-based tokens, role-based access control, comprehensive security middleware, and robust error handling, it provides enterprise-grade security while maintaining ease of use and integration.

### Key Benefits
- 🔐 **Secure by Default** - Multiple layers of security protection
- 🚀 **High Performance** - Optimized for microservices architecture
- 🛠️ **Developer Friendly** - Comprehensive testing and documentation
- 📈 **Scalable** - Designed for horizontal scaling
- 🔧 **Configurable** - Flexible configuration options
- 🏥 **Observable** - Built-in health monitoring and logging

For implementation details, refer to the source code and test files in the repository.