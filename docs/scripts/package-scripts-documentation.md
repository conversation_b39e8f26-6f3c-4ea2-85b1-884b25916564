# Introspection.Finance.Identity - Package Scripts Documentation

## Overview

This document provides comprehensive documentation for all npm/bun scripts
available in the Identity Provider (IdP) microservice.

## 🚀 Development Scripts

### Server Management

| Script             | Command                                          | Description                        |
| ------------------ | ------------------------------------------------ | ---------------------------------- |
| `dev`              | `NODE_ENV=development bun --watch src/server.ts` | Development server with hot reload |
| `dev:local`        | `NODE_ENV=local bun --watch src/server.ts`       | Local development server           |
| `start:staging`    | `NODE_ENV=staging bun --watch src/server.ts`     | Staging server with hot reload     |
| `start:production` | `NODE_ENV=production bun run src/server.ts`      | Production server (no hot reload)  |

## 🔍 Code Quality & Type Checking

### TypeScript

| Script        | Command            | Description                       |
| ------------- | ------------------ | --------------------------------- |
| `log:tsc`     | `npx tsc --noEmit` | Type check without emitting files |
| `check:types` | `npx tsc --noEmit` | Alias for type checking           |

### ESLint - Basic

| Script        | Command                     | Description                     |
| ------------- | --------------------------- | ------------------------------- |
| `lint`        | `eslint`                    | Basic linting                   |
| `lint:errors` | `eslint . --max-warnings 0` | Lint with zero warnings allowed |
| `lint:fix`    | `eslint . --fix`            | Auto-fix linting issues         |

### ESLint - Advanced

| Script             | Command                                                   | Description                            |
| ------------------ | --------------------------------------------------------- | -------------------------------------- |
| `lint:strict`      | `eslint . --max-warnings 0`                               | Strict linting (zero warnings)         |
| `lint:errors-only` | `eslint . --quiet`                                        | Show only errors, suppress warnings    |
| `lint:dry-fix`     | `eslint . --fix-dry-run --format compact`                 | Preview fixes without applying         |
| `lint:output:json` | `eslint . --format json`                                  | JSON output for CI/CD integration      |
| `lint:cache`       | `eslint . --cache`                                        | Use caching for faster subsequent runs |
| `lint:report:html` | `eslint . --format html --output-file eslint-report.html` | Generate HTML report                   |

### ESLint - Output Formats

| Script                  | Command                       | Description                       |
| ----------------------- | ----------------------------- | --------------------------------- |
| `lint:compact-output`   | `eslint . --format compact`   | Concise one-line-per-issue output |
| `lint:codeframe-output` | `eslint . --format codeframe` | Show code context around errors   |
| `lint:cache-purge`      | `rm -f .eslintcache`          | Clear ESLint cache                |

### Prettier

| Script         | Command              | Description                |
| -------------- | -------------------- | -------------------------- |
| `check:format` | `prettier --check .` | Check code formatting      |
| `format:fix`   | `prettier --write .` | Auto-fix formatting issues |

## 🧪 Testing Scripts

### Individual Test Files

| Script             | Command                                      | Description                         | Test File                     |
| ------------------ | -------------------------------------------- | ----------------------------------- | ----------------------------- |
| `test:auth`        | `bun test tests/authentication.test.ts`      | Authentication & registration tests | `authentication.test.ts`      |
| `test:health`      | `bun test tests/health.test.ts`              | Health check endpoint tests         | `health.test.ts`              |
| `test:tokens`      | `bun test tests/token-management.test.ts`    | Token management tests              | `token-management.test.ts`    |
| `test:security`    | `bun test tests/security.test.ts`            | Security-related tests              | `security.test.ts`            |
| `test:identity`    | `bun test tests/identity-management.test.ts` | Identity management tests           | `identity-management.test.ts` |
| `test:session`     | `bun test tests/session.test.ts`             | Session management tests            | `session.test.ts`             |
| `test:integration` | `bun test tests/integration.test.ts`         | Integration tests                   | `integration.test.ts`         |

### Test Execution Control

| Script          | Command                      | Description                    |
| --------------- | ---------------------------- | ------------------------------ |
| `test:all`      | `bun test tests/`            | Run all tests                  |
| `test:watch`    | `bun test tests/ --watch`    | Run tests in watch mode        |
| `test:coverage` | `bun test tests/ --coverage` | Run tests with coverage report |

### Test Utilities

| Script            | Command                                              | Description                               | Status       |
| ----------------- | ---------------------------------------------------- | ----------------------------------------- | ------------ |
| `test:list-names` | `find tests -name '*.test.ts' -exec basename {} \\;` | List all test file names without running  | ✅ **FIXED** |
| `test:count`      | `find tests -name '*.test.ts' \| wc -l`              | Count total number of test files          | 🆕 **NEW**   |
| `test:file`       | `bun test`                                           | Run specific test file (append file path) | 🆕 **NEW**   |
| `test:grep`       | `bun test --grep`                                    | Search for specific test patterns         | 🆕 **NEW**   |
| `test:filter`     | `bun test --filter`                                  | Filter tests by suite name                | 🆕 **NEW**   |
| `test:debug`      | `bun --inspect-brk test`                             | Debug tests with inspector                | 🆕 **NEW**   |
| `test:silent`     | `bun test --silent`                                  | Run tests with minimal output             | 🆕 **NEW**   |
| `test:verbose`    | `bun test --verbose`                                 | Run tests with detailed output            | 🆕 **NEW**   |
| `test:bail`       | `bun test --bail`                                    | Stop on first test failure                | ✅ Working   |
| `test:failed`     | `bun test --only-failed`                             | Re-run only failed tests                  | ✅ Working   |
| `test:todo`       | `bun test --todo`                                    | Show tests marked as `.todo()`            | ✅ Working   |
| `test:focus`      | `bun test --only`                                    | Run only tests marked with `.only()`      | ✅ Working   |

### Deprecated Tests

| Script                       | Command                                        | Description             |
| ---------------------------- | ---------------------------------------------- | ----------------------- |
| `test:deprecated:middleware` | `bun test tests/deprecated/middleware.test.ts` | Legacy middleware tests |

## 🛠️ Utility Scripts

### Project Management

| Script          | Command                                           | Description                |
| --------------- | ------------------------------------------------- | -------------------------- |
| `reinstall`     | `bun run src/config/scripts/cli.ts reinstall`     | Reinstall dependencies     |
| `clean:imports` | `bun run src/config/scripts/cli.ts clean:imports` | Clean up import statements |

### Code Mapping & Analysis

| Script            | Command                                                                    | Description                   |
| ----------------- | -------------------------------------------------------------------------- | ----------------------------- |
| `map:all`         | `bun run src/config/scripts/cli.ts map`                                    | Map entire codebase structure |
| `map:folders`     | `bun run src/config/scripts/cli.ts map --folders-only`                     | Map folder structure only     |
| `map:hide`        | `bun run src/config/scripts/cli.ts map --show-all-with-hide-list`          | Map with hidden items shown   |
| `map:sonnet:hide` | `bun run src/config/scripts/cli.ts map:sonnet . --show-all-with-hide-list` | Sonnet-specific mapping       |

## ✅ Recent Fixes & Improvements

### Fixed Scripts

- **`test:list-names`**: Now properly lists test files without running them
- **Added 7 new test utility scripts** for better development workflow

### New Test Scripts Added

- `test:count` - Count total test files
- `test:file` - Run specific test file
- `test:grep` - Search test patterns
- `test:filter` - Filter by suite name
- `test:debug` - Debug with inspector
- `test:silent` - Minimal output
- `test:verbose` - Detailed output

## 📋 Test File Structure

```
tests/
├── authentication.test.ts          # Auth & registration validation
├── health.test.ts                  # Health endpoint tests
├── identity-management.test.ts     # User management operations
├── integration.test.ts             # End-to-end integration tests
├── security.test.ts                # Security & vulnerability tests
├── session.test.ts                 # Session management tests
├── token-management.test.ts        # JWT token operations
├── token-expiration.test.ts        # Token expiration handling
├── session-middleware.test.ts      # Session middleware tests
├── constants/                      # Test constants and types
├── helpers/                        # Test helper utilities
└── deprecated/                     # Legacy test files
```

## 🎯 Recommended Usage Patterns

### Development Workflow

```bash
# Start development server
bun run dev

# Run type checking
bun run check:types

# Fix linting issues
bun run lint:fix

# Run specific test suite
bun run test:auth

# Run tests in watch mode
bun run test:watch
```

### CI/CD Pipeline

```bash
# Type checking
bun run check:types

# Strict linting (zero warnings)
bun run lint:strict

# Run all tests with coverage
bun run test:coverage

# Generate HTML lint report
bun run lint:report:html
```

### Debugging

```bash
# Run tests and stop on first failure
bun run test:bail

# Re-run only failed tests
bun run test:failed

# Check code formatting
bun run check:format
```

## 📈 Usage Statistics

### Test Files Overview

- **Total Test Files**: 10
- **Individual Test Scripts**: 7
- **Utility Test Scripts**: 12
- **Deprecated Tests**: 1

### Most Useful Scripts for Development

1. `test:list-names` - Quick overview of all tests
2. `test:auth` - Most frequently used test suite
3. `test:bail` - Fast failure detection
4. `test:failed` - Efficient debugging workflow
5. `test:debug` - Deep debugging capabilities
