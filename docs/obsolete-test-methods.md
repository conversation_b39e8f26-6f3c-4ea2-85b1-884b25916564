> Removed these as they are not being used, may be useful in the future

```ts
export class TokenAnalysisHelper {
  /** Perform comprehensive token analysis for both access and refresh tokens */
  static analyzeTokenPair(
    tokens: AuthTokens,
    context: string = 'Token Pair'
  ): void {
    testLogger.log(`\n🔍 === ${context.toUpperCase()} ANALYSIS ===`);

    if (tokens.accessToken) {
      logTokenAnalysis(tokens.accessToken, `Access Token from ${context}`);
    }
    if (tokens.refreshToken) {
      logTokenAnalysis(tokens.refreshToken, `Refresh Token from ${context}`);
    }

    this.compareTokens(tokens);
  }

  /** Compare access and refresh tokens side by side */
  static compareTokens(tokens: AuthTokens): void {
    if (!tokens.accessToken || !tokens.refreshToken) {
      testLogger.log(
        '⚠️ Cannot compare tokens - missing access or refresh token'
      );
      return;
    }

    testLogger.info('\n🔄 === COMPARING ACCESS vs REFRESH TOKENS ===');

    const accessAnalysis = analyzeToken(tokens.accessToken);
    const refreshAnalysis = analyzeToken(tokens.refreshToken);

    if (accessAnalysis.isValid && refreshAnalysis.isValid) {
      testLogger.info('\n🔄 === COMPARING ACCESS TOKEN vs REFRESH TOKEN ===');
      testLogger.info(`Validity: ✅ vs ✅`);
      testLogger.info(
        `User ID: ${accessAnalysis.payload?.userId} vs ${refreshAnalysis.payload?.userId}`
      );
      testLogger.info(
        `Email: ${accessAnalysis.payload?.email} vs ${refreshAnalysis.payload?.email}`
      );
      testLogger.info(
        `Token Type: ${accessAnalysis.payload?.tokenType} vs ${refreshAnalysis.payload?.tokenType}`
      );
      testLogger.info(
        `Remaining Time: ${this.formatTime(accessAnalysis.payload?.exp)} vs ${this.formatTime(refreshAnalysis.payload?.exp)}`
      );
      testLogger.info('=== END COMPARISON ===');

      // Detailed comparison
      const accessLifetime = this.getTokenLifetime(accessAnalysis.payload?.exp);
      const refreshLifetime = this.getTokenLifetime(
        refreshAnalysis.payload?.exp
      );

      testLogger.info('\n📊 Detailed Comparison:');
      testLogger.info(`   Access Token Lifetime: ${accessLifetime}`);
      testLogger.info(`   Refresh Token Lifetime: ${refreshLifetime}`);
      testLogger.info(
        `   Same User: ${accessAnalysis.payload?.userId === refreshAnalysis.payload?.userId ? '✅' : '❌'}`
      );
      testLogger.info(
        `   Same Client: ${accessAnalysis.payload?.clientId === refreshAnalysis.payload?.clientId ? '✅' : '❌'}`
      );
      testLogger.info('=== END TOKEN COMPARISON ===');
    }
  }

  /** Log registration response data in a structured way */
  static logRegistrationResponse(
    responseData: any,
    context: string = 'Registration'
  ): void {
    testLogger.info(`📊 ${context} Response Data:`, {
      hasToken: !!responseData.token,
      hasRefreshToken: !!responseData.refreshToken,
      hasUser: !!responseData.user,
      tokenLength: responseData.token?.length || 0,
      refreshTokenLength: responseData.refreshToken?.length || 0,
    });
  }

  /** Format time remaining for token expiration */
  private static formatTime(exp?: number): string {
    if (!exp) return 'unknown';

    const now = Math.floor(Date.now() / 1000);
    const remaining = exp - now;

    if (remaining <= 0) return 'expired';

    const minutes = Math.floor(remaining / 60);
    return `${minutes}m`;
  }

  /** Get token lifetime in a readable format */
  private static getTokenLifetime(exp?: number): string {
    if (!exp) return 'unknown';

    const now = Math.floor(Date.now() / 1000);
    const remaining = exp - now;

    if (remaining <= 0) return 'expired';

    const minutes = Math.floor(remaining / 60);
    return `${minutes} minutes`;
  }
}
```

**Types**

```ts
export interface TokenVerificationResult {
  isValid: boolean;
  payload?: AccessTokenPayload | RefreshTokenPayload;
  error?: string;
}

/** Authenticated request helper interface */
export interface AuthenticatedRequestHelper {
  get: (url: string) => Promise<Response>;
  post: (url: string) => Promise<Response>;
  put: (url: string) => Promise<Response>;
  delete: (url: string) => Promise<Response>;
  patch: (url: string) => Promise<Response>;
}

/** Test response types using SharedInternals response types */
export type TestApiResponse<T = any> = ApiResponse<T>;
export type TestSuccessResponse<T = any> = SuccessResponse<T>;
export type TestErrorResponse = ErrorResponse;
export type TestTokenResponse = TokenResponse;

/** Test context for tracking test operations */
export interface TestContext {
  testName: string;
  createdUsers: string[];
  adminToken?: string;
  startTime: number;
}
```

```ts
export class AuthTestHelper {
  // Track created users for cleanup
  private static createdUsers: string[] = [];

  // Admin user for cleanup operations
  private static adminUser: {
    email: string;
    password: string;
    clientId: string;
    clientOrigin: number;
    accessToken?: string;
  } | null = null;

  /** Login with existing credentials */
  static async login(
    email: string,
    password: string
  ): Promise<AuthTokens | null> {
    try {
      // Wait to avoid rate limiting
      await this.wait(1000);

      const loginResponse = await request
        .post('/api/v1/auth/login')
        .send({ email, password });

      if (
        ![200, 201].includes(loginResponse.status) ||
        !loginResponse.body.data?.token
      ) {
        testLogger.error(
          'Login failed:',
          loginResponse.status,
          loginResponse.body
        );
        return null;
      }

      return {
        accessToken: loginResponse.body.data.token,
        refreshToken: loginResponse.body.data.refreshToken || '',
        user: loginResponse.body.data.user,
      } as AuthTokens;
    } catch (error) {
      testLogger.error('Error in login:', error);
      return null;
    }
  }

  /**
   * Verify a token is valid using the API endpoint
   * Returns detailed verification result
   */
  static async verifyToken(
    token: string
  ): Promise<import('./test.types').TokenVerificationResult> {
    try {
      testLogger.log('🔍 Verifying token via API endpoint...');

      const response = await request
        .post('/api/v1/verify-access-token')
        .send({ token });

      testLogger.log(`Token verification response: ${response.status}`);

      const isValid =
        response.status === HttpStatus.OK &&
        response.body.data?.active === true;

      if (isValid) {
        testLogger.log('✅ Token is valid');

        // Also analyze the token locally for additional info
        const { analyzeToken } = await import('excelytics.shared-internals');
        const analysis = analyzeToken(token);
        return {
          isValid: true,
          payload: analysis.payload,
          error: undefined,
        };
      } else {
        testLogger.log('❌ Token is invalid');
        return {
          isValid: false,
          payload: undefined,
          error: response.body?.error?.message || 'Token verification failed',
        };
      }
    } catch (error: any) {
      testLogger.error('❌ Error during token verification:', error.message);
      return {
        isValid: false,
        payload: undefined,
        error: error.message,
      };
    }
  }

  /** Test token expiration by attempting to use an expired token */
  static async testTokenExpiration(expiredToken: string): Promise<boolean> {
    testLogger.log('⏰ Testing token expiration...');
    try {
      const response = await request
        .get('/api/v1/protected-endpoint')
        .set('Authorization', `Bearer ${expiredToken}`);

      const isExpired = response.status === 401;
      testLogger.log(
        `Token expiration test: ${isExpired ? '✅ Properly expired' : '❌ Still valid'}`
      );

      return isExpired;
    } catch (error: any) {
      testLogger.error('❌ Error testing token expiration:', error.message);
      return false;
    }
  }

  /** Test malformed token handling */
  static async testMalformedToken(malformedToken: string): Promise<boolean> {
    testLogger.log('🔧 Testing malformed token handling...');
    try {
      const response = await request
        .get('/api/v1/protected-endpoint')
        .set('Authorization', `Bearer ${malformedToken}`);

      const isRejected = response.status === 401;
      testLogger.log(
        `Malformed token test: ${isRejected ? '✅ Properly rejected' : '❌ Incorrectly accepted'}`
      );

      return isRejected;
    } catch (error: any) {
      testLogger.error('❌ Error testing malformed token:', error.message);
      return false;
    }
  }

  /** Test missing authorization header */
  static async testMissingAuth(): Promise<boolean> {
    testLogger.log('🚫 Testing missing authorization header...');
    try {
      const response = await request.get('/api/v1/protected-endpoint');

      const isRejected = response.status === 401;
      testLogger.log(
        `Missing auth test: ${isRejected ? '✅ Properly rejected' : '❌ Incorrectly allowed'}`
      );

      return isRejected;
    } catch (error: any) {
      testLogger.error('❌ Error testing missing auth:', error.message);
      return false;
    }
  }

  /**
   * Create Identity model instance for testing
   * @param emailPrefix - Prefix for the email
   * @returns Identity model instance (not saved)
   */
  static createIdentityInstance(emailPrefix: string = 'test'): Identity {
    const userData = this.createTestUserData(emailPrefix);
    return new IdentityModel(userData);
  }

  /** Create an authenticated request helper with token */
  static createAuthenticatedRequest(
    token: string
  ): import('./test.types').AuthenticatedRequestHelper {
    testLogger.log('🔐 Creating authenticated request helper');

    return {
      get: async (url: string) => {
        testLogger.log(`🔐 GET ${url} with auth`);
        return request.get(url).set('Authorization', `Bearer ${token}`);
      },
      post: async (url: string) => {
        testLogger.log(`🔐 POST ${url} with auth`);
        return request.post(url).set('Authorization', `Bearer ${token}`);
      },
      put: async (url: string) => {
        testLogger.log(`🔐 PUT ${url} with auth`);
        return request.put(url).set('Authorization', `Bearer ${token}`);
      },
      delete: async (url: string) => {
        testLogger.log(`🔐 DELETE ${url} with auth`);
        return request.delete(url).set('Authorization', `Bearer ${token}`);
      },
      patch: async (url: string) => {
        testLogger.log(`🔐 PATCH ${url} with auth`);
        return request.patch(url).set('Authorization', `Bearer ${token}`);
      },
    };
  }

  /** Comprehensive security test suite */
  static async runSecurityTests(
    validToken: string,
    expiredToken: string,
    malformedToken: string
  ): Promise<{
    tokenVerification: boolean;
    expiredTokenRejection: boolean;
    malformedTokenRejection: boolean;
    missingAuthRejection: boolean;
  }> {
    testLogger.log('🛡️ Running comprehensive security test suite...');
    const results = {
      tokenVerification: false,
      expiredTokenRejection: false,
      malformedTokenRejection: false,
      missingAuthRejection: false,
    };

    // Test valid token
    const verificationResult = await this.verifyToken(validToken);
    results.tokenVerification = verificationResult.isValid;

    // Test expired token
    results.expiredTokenRejection =
      await this.testTokenExpiration(expiredToken);

    // Test malformed token
    results.malformedTokenRejection =
      await this.testMalformedToken(malformedToken);

    // Test missing auth
    results.missingAuthRejection = await this.testMissingAuth();

    testLogger.log('🛡️ Security test suite completed:', results);

    return results;
  }
}
```
