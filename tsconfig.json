{
  "compilerOptions": {
    // Language and Environment
    "typeRoots": ["./node_modules/@types", "./types"],
    "types": ["bun-types", "node"],
    "lib": ["ESNext", "DOM"],
    "target": "ESNext",

    // Modules
    "module": "ESNext",
    "moduleDetection": "force",
    "rootDir": ".",
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*", "tests/*"],
      "@app-types/*": ["types/*"]
    },

    // Bundler Mode
    "allowImportingTsExtensions": true,
    "moduleResolution": "bundler",
    "verbatimModuleSyntax": true,
    "noEmit": true,

    // Interop Constraints
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,

    // Type Checking & Completeness
    "strict": true,
    "skipLibCheck": true /* Skip type checking all .d.ts files. */,
    "resolveJsonModule": true,
    "skipDefaultLibCheck": true /* Skip type checking .d.ts files that are included with TypeScript. */,
    "noFallthroughCasesInSwitch": true,

    // Enable Typegoose Decorator usage
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "strictPropertyInitialization": false
  },
  "include": ["src/**/*", "types/**/*", "tests/**/*", "src/server.ts"],
  "exclude": ["node_modules", "dist", "shared"]
}
