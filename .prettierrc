{"$schema": "https://json.schemastore.org/prettierrc", "semi": true, "tabWidth": 4, "useTabs": true, "printWidth": 120, "singleQuote": true, "quoteProps": "as-needed", "trailingComma": "none", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "experimentalTernaries": false, "endOfLine": "lf", "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "embeddedLanguageFormatting": "auto", "singleAttributePerLine": false, "requirePragma": false, "insertPragma": false, "overrides": [{"files": ["*.json", "*.jsonc", "tsconfig*.json"], "options": {"tabWidth": 4, "useTabs": true}}, {"files": ["*.md", "*.mdx"], "options": {"tabWidth": 4, "useTabs": true, "printWidth": 140, "proseWrap": "never"}}, {"files": ["*.yml", "*.yaml"], "options": {"tabWidth": 2, "useTabs": false}}]}