{"$schema": "https://json.schemastore.org/prettierrc", "semi": true, "tabWidth": 4, "useTabs": true, "printWidth": 120, "singleQuote": true, "quoteProps": "as-needed", "trailingComma": "es5", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "embeddedLanguageFormatting": "auto", "singleAttributePerLine": false, "overrides": [{"files": ["*.json", "*.jsonc"], "options": {"tabWidth": 2, "useTabs": false}}, {"files": ["*.md", "*.mdx"], "options": {"tabWidth": 2, "useTabs": false, "printWidth": 80, "proseWrap": "always"}}, {"files": ["*.yml", "*.yaml"], "options": {"tabWidth": 2, "useTabs": false}}, {"files": ["package.json"], "options": {"tabWidth": 4, "useTabs": false}}]}