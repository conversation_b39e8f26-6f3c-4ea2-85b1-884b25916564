reza@Rezas-<PERSON><PERSON>ook-Pro Introspection.Finance.Calc % bun install
[0.58ms] ".env"
bun install v1.2.5 (013fdddc)

+ excelytics.shared-internals@1.6.10

2 packages installed [3.64s]
reza@Rezas-MacBook-Pro Introspection.Finance.Calc % bun dev
$ NODE_ENV=development bun --watch src/server.ts
-- Connected to MongoDB in development mode
🚀 Server: excelytics.calc is running on port 6003
📊 Environment: development
🔗 API Base: /api/v1
💾 Log Level: DEBUG
🏥 Health Check: http://localhost:6003/api/v1/health
🏥 Deeper Health Check: http://localhost:6003/api/v1/health/all
------------------------------------------------------------------------------

--- Global Error Handler Triggered ---
[Request Path]: GET /
[Timestamp]: 29-06-2025 12:15am SAST
[Error Type]: Error
[Message]: The requested resource GET / was not found on this server
[Stack REDACTED] - Set LOG_SENSITIVE=true to view
[Error Type]: Unknown/Standard Error
Message: The requested resource GET / was not found on this server
Stack: Error: The requested resource GET / was not found on this server
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/src/server.ts:38:23)                                                                   
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/src/middleware/index.ts:69:3)                                                          
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at logger (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/morgan/index.js:144:5)                                                         
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/src/middleware/index.ts:37:3)                                                          
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at urlencodedParser (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/body-parser/lib/types/urlencoded.js:68:7)                            
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at jsonParser (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/body-parser/lib/types/json.js:100:7)                                       
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at cors (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:188:7)                                                         
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:224:17)                                                 
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:219:13)                                                 
at optionsCallback (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:199:9)                                              
at corsMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:204:7)                                               
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:527:6)                                                  
at xXssProtectionMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:307:3)                                      
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xPoweredByMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:300:3)                                          
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xPermittedCrossDomainPoliciesMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:293:3)                       
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xDownloadOptionsMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:257:3)                                    
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xDnsPrefetchControlMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:250:3)                                 
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xContentTypeOptionsMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:242:3)                                 
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at strictTransportSecurityMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:235:3)                             
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at referrerPolicyMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:203:3)                                      
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at originAgentClusterMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:178:3)                                  
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at crossOriginResourcePolicyMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:171:3)                           
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at crossOriginOpenerPolicyMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:155:3)                             
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at contentSecurityPolicyMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:120:4)                               
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at helmetMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:527:11)                                             
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/src/middleware/custom.middleware.ts:21:3)                                              
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at handle (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:186:3)                                                         
at handle (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/express/lib/application.js:177:15)                                             
at app (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/express/lib/express.js:38:9)                                                      
at emit (node:events:98:22)                                                                         
at onNodeHTTPRequest (node:_http_server:633:24)                                                     
GET / 500 15.427 ms - 226
--- Global Error Handler Triggered ---
[Request Path]: GET /
[Timestamp]: 29-06-2025 12:15am SAST
[Error Type]: Error
[Message]: The requested resource GET / was not found on this server
[Stack REDACTED] - Set LOG_SENSITIVE=true to view
[Error Type]: Unknown/Standard Error
Message: The requested resource GET / was not found on this server
Stack: Error: The requested resource GET / was not found on this server
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/src/server.ts:38:23)                                                                   
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/src/middleware/index.ts:69:3)                                                          
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at logger (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/morgan/index.js:144:5)                                                         
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/src/middleware/index.ts:37:3)                                                          
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at urlencodedParser (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/body-parser/lib/types/urlencoded.js:68:7)                            
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at jsonParser (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/body-parser/lib/types/json.js:100:7)                                       
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at cors (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:188:7)                                                         
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:224:17)                                                 
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:219:13)                                                 
at optionsCallback (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:199:9)                                              
at corsMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:204:7)                                               
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:527:6)                                                  
at xXssProtectionMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:307:3)                                      
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xPoweredByMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:300:3)                                          
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xPermittedCrossDomainPoliciesMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:293:3)                       
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xDownloadOptionsMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:257:3)                                    
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xDnsPrefetchControlMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:250:3)                                 
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xContentTypeOptionsMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:242:3)                                 
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at strictTransportSecurityMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:235:3)                             
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at referrerPolicyMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:203:3)                                      
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at originAgentClusterMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:178:3)                                  
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at crossOriginResourcePolicyMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:171:3)                           
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at crossOriginOpenerPolicyMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:155:3)                             
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at contentSecurityPolicyMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:120:4)                               
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at helmetMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:527:11)                                             
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/src/middleware/custom.middleware.ts:21:3)                                              
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at handle (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:186:3)                                                         
at handle (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/express/lib/application.js:177:15)                                             
at app (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/express/lib/express.js:38:9)                                                      
at emit (node:events:98:22)                                                                         
at onNodeHTTPRequest (node:_http_server:633:24)                                                     
GET / 500 1.896 ms - 226
--- Global Error Handler Triggered ---
[Request Path]: GET /
[Timestamp]: 29-06-2025 12:15am SAST
[Error Type]: Error
[Message]: The requested resource GET / was not found on this server
[Stack REDACTED] - Set LOG_SENSITIVE=true to view
[Error Type]: Unknown/Standard Error
Message: The requested resource GET / was not found on this server
Stack: Error: The requested resource GET / was not found on this server
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/src/server.ts:38:23)                                                                   
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/src/middleware/index.ts:69:3)                                                          
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at logger (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/morgan/index.js:144:5)                                                         
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/src/middleware/index.ts:37:3)                                                          
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at urlencodedParser (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/body-parser/lib/types/urlencoded.js:68:7)                            
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at jsonParser (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/body-parser/lib/types/json.js:100:7)                                       
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at cors (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:188:7)                                                         
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:224:17)                                                 
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:219:13)                                                 
at optionsCallback (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:199:9)                                              
at corsMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:204:7)                                               
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:527:6)                                                  
at xXssProtectionMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:307:3)                                      
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xPoweredByMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:300:3)                                          
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xPermittedCrossDomainPoliciesMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:293:3)                       
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xDownloadOptionsMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:257:3)                                    
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xDnsPrefetchControlMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:250:3)                                 
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xContentTypeOptionsMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:242:3)                                 
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at strictTransportSecurityMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:235:3)                             
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at referrerPolicyMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:203:3)                                      
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at originAgentClusterMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:178:3)                                  
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at crossOriginResourcePolicyMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:171:3)                           
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at crossOriginOpenerPolicyMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:155:3)                             
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at contentSecurityPolicyMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:120:4)                               
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at helmetMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:527:11)                                             
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/src/middleware/custom.middleware.ts:21:3)                                              
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at handle (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:186:3)                                                         
at handle (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/express/lib/application.js:177:15)                                             
at app (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/express/lib/express.js:38:9)                                                      
at emit (node:events:98:22)                                                                         
at onNodeHTTPRequest (node:_http_server:633:24)                                                     
GET / 500 1.302 ms - 226
--- Global Error Handler Triggered ---
[Request Path]: GET /
[Timestamp]: 29-06-2025 12:15am SAST
[Error Type]: Error
[Message]: The requested resource GET / was not found on this server
[Stack REDACTED] - Set LOG_SENSITIVE=true to view
[Error Type]: Unknown/Standard Error
Message: The requested resource GET / was not found on this server
Stack: Error: The requested resource GET / was not found on this server
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/src/server.ts:38:23)                                                                   
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/src/middleware/index.ts:69:3)                                                          
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at logger (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/morgan/index.js:144:5)                                                         
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/src/middleware/index.ts:37:3)                                                          
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at urlencodedParser (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/body-parser/lib/types/urlencoded.js:68:7)                            
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at jsonParser (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/body-parser/lib/types/json.js:100:7)                                       
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at cors (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:188:7)                                                         
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:224:17)                                                 
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:219:13)                                                 
at optionsCallback (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:199:9)                                              
at corsMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/cors/lib/index.js:204:7)                                               
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:527:6)                                                  
at xXssProtectionMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:307:3)                                      
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xPoweredByMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:300:3)                                          
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xPermittedCrossDomainPoliciesMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:293:3)                       
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xDownloadOptionsMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:257:3)                                    
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xDnsPrefetchControlMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:250:3)                                 
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at xContentTypeOptionsMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:242:3)                                 
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at strictTransportSecurityMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:235:3)                             
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at referrerPolicyMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:203:3)                                      
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at originAgentClusterMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:178:3)                                  
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at crossOriginResourcePolicyMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:171:3)                           
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at crossOriginOpenerPolicyMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:155:3)                             
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at contentSecurityPolicyMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:120:4)                               
at internalNext (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:525:6)                                                  
at helmetMiddleware (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/helmet/index.mjs:527:11)                                             
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/src/middleware/custom.middleware.ts:21:3)                                              
at handleRequest (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/lib/layer.js:152:17)                                             
at trimPrefix (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:342:13)                                                    
at <anonymous> (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:297:9)                                                    
at next (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:291:5)                                                           
at handle (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/router/index.js:186:3)                                                         
at handle (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/express/lib/application.js:177:15)                                             
at app (/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos/Introspection.Finance.Calc/node_modules/express/lib/express.js:38:9)                                                      
at emit (node:events:98:22)                                                                         
at onNodeHTTPRequest (node:_http_server:633:24)                                                     
GET / 500 1.725 ms - 226