{"name": "introspection.finance.identity", "description": "Identity Provider for Introspection.Identity", "version": "1.1.2", "type": "module", "author": "<PERSON><PERSON>", "scripts": {"dev": "NODE_ENV=development bun --watch src/server.ts", "dev:local": "NODE_ENV=local bun --watch src/server.ts", "start:staging": "NODE_ENV=staging bun --watch src/server.ts", "start:production": "NODE_ENV=production bun run src/server.ts", "log:tsc": "npx tsc --noEmit", "lint": "eslint", "lint:errors": "eslint . --max-warnings 0", "lint:fix": "eslint . --fix", "check:types": "npx tsc --noEmit", "lint:strict": "eslint . --max-warnings 0", "lint:errors-only": "eslint . --quiet", "lint:dry-fix": "eslint . --fix-dry-run --format compact", "lint:output:json": "eslint . --format json", "lint:cache": "eslint . --cache", "lint:report:html": "eslint . --format html --output-file eslint-report.html", "lint:compact-output": "eslint . --format compact", "lint:codeframe-output": "eslint . --format codeframe", "lint:cache-purge": "rm -f .es<PERSON>", "check:format": "prettier --check .", "format:fix": "prettier --write .", "test:deprecated:middleware": "bun test tests/deprecated/middleware.test.ts", "test:auth": "bun test tests/authentication.test.ts", "test:health": "bun test tests/health.test.ts", "test:tokens": "bun test tests/token-management.test.ts", "test:security": "bun test tests/security.test.ts", "test:identity": "bun test tests/identity-management.test.ts", "test:session": "bun test tests/session.test.ts", "test:integration": "bun test tests/integration.test.ts", "test:all": "bun test tests/", "test:watch": "bun test tests/ --watch", "test:coverage": "bun test tests/ --coverage", "test:list-names": "find tests -name '*.test.ts' -exec basename {} \\;", "test:count": "find tests -name '*.test.ts' | wc -l", "test:file": "bun test", "test:grep": "bun test --grep", "test:filter": "bun test --filter", "test:debug": "bun --inspect-brk test", "test:silent": "bun test --silent", "test:verbose": "bun test --verbose", "test:bail": "bun test --bail", "test:failed": "bun test --only-failed", "test:todo": "bun test --todo", "test:focus": "bun test --only", "reinstall": "bun run src/config/scripts/cli.ts reinstall", "clean:imports": "bun run src/config/scripts/cli.ts clean:imports", "map:all": "bun run src/config/scripts/cli.ts map", "map:folders": "bun run src/config/scripts/cli.ts map --folders-only", "map:hide": "bun run src/config/scripts/cli.ts map --show-all-with-hide-list", "map:sonnet:hide": "bun run src/config/scripts/cli.ts map:sonnet . --show-all-with-hide-list"}, "dependencies": {"@typegoose/typegoose": "^12.17.0", "@types/cookie-parser": "^1.4.9", "@types/express-session": "^1.18.2", "@types/supertest": "^6.0.3", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "compression": "^1.8.0", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "excelytics.shared-internals": "^1.6.11", "excelytics.shared-models": "0.3.20", "express": "^5.1.0", "express-async-handler": "^1.2.0", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "8.16.0", "morgan": "^1.10.0", "nanoid": "^5.1.5", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/express-validator": "^2.20.33", "@types/helmet": "^0.0.48", "@types/jsonwebtoken": "^9.0.10", "@types/mongoose": "^5.11.97", "@types/morgan": "^1.9.10", "@types/uuid": "^10.0.0", "bun": "^1.2.16", "bun-types": "^1.2.16", "eslint": "^9.29.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-perfectionist": "^4.15.0", "eslint-plugin-prettier": "^5.5.0", "globals": "^15.15.0", "prettier": "^3.5.3", "supertest": "^7.1.1", "tsconfig-paths": "^4.2.0", "typescript-eslint": "^8.34.1"}}